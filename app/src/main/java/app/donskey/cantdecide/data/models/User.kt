package app.donskey.cantdecide.data.models

import com.google.firebase.Timestamp
import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName
import com.google.firebase.firestore.ServerTimestamp

/**
 * User data class representing a user in the application.
 * This class maps to the users collection in Firestore.
 */
data class User(
    @DocumentId
    val uid: String = "",

    @PropertyName("displayName")
    val displayName: String = "",

    @PropertyName("phoneNumber")
    val phoneNumber: String = "",

    @PropertyName("email")
    val email: String = "",

    @PropertyName("photoUrl")
    val photoUrl: String = "",

    @PropertyName("bio")
    val bio: String = "",

    @PropertyName("allowTagging")
    val allowTagging: Boolean = true,

    @PropertyName("profileVisibility")
    val profileVisibility: String = "public",

    @PropertyName("hideOnlineStatus")
    val hideOnlineStatus: Boolean = false,

    @PropertyName("publicProfile")
    val publicProfile: Boolean = false,

    @PropertyName("fcmToken")
    val fcmToken: String = "",

    @ServerTimestamp
    @PropertyName("createdAt")
    val createdAt: Timestamp? = null,

    @ServerTimestamp
    @PropertyName("lastActive")
    val lastActive: Timestamp? = null,

    @PropertyName("pollsCreated")
    val pollsCreated: Int = 0,

    @PropertyName("pollsVotedOn")
    val pollsVotedOn: Int = 0,

    @PropertyName("votesReceived")
    val votesReceived: Int = 0,

    @PropertyName("decisionsCount")
    val decisionsCount: Int = 0,

    @PropertyName("friendsCount")
    val friendsCount: Int = 0,

    @PropertyName("pendingRequestCount")
    val pendingRequestCount: Int = 0,

    @PropertyName("notificationSettings")
    val notificationSettings: NotificationSettings = NotificationSettings(),

    @PropertyName("themePreferences")
    val themePreferences: ThemePreferences = ThemePreferences()
)

/**
 * Notification settings data class representing user notification preferences.
 */
data class NotificationSettings(
    @PropertyName("pollInvitations")
    val pollInvitations: Boolean = true,

    @PropertyName("voteNotifications")
    val voteNotifications: Boolean = true,

    @PropertyName("pollResults")
    val pollResults: Boolean = true,

    @PropertyName("commentNotifications")
    val commentNotifications: Boolean = true,

    @PropertyName("appUpdates")
    val appUpdates: Boolean = true,

    @PropertyName("notifyOnFriendRequest")
    val notifyOnFriendRequest: Boolean = true
)

/**
 * Theme preferences data class representing user theme settings.
 */
data class ThemePreferences(
    @PropertyName("darkMode")
    val darkMode: Boolean = false,

    @PropertyName("themeColorIndex")
    val themeColorIndex: Int = 1 // Default to Blue (index 1)
)