package app.donskey.cantdecide.data.repositories

import android.util.Log
import app.donskey.cantdecide.data.models.User
import app.donskey.cantdecide.firebase.FirebaseHelper
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.channels.awaitClose
import com.google.firebase.firestore.EventListener
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.FirebaseFirestoreException
import com.google.firebase.firestore.FieldPath

/**
 * Repository for managing user data in Firestore.
 * Handles operations like creating users, fetching user profiles, and updating user data.
 */
@Singleton // Make UserRepository a Singleton
class UserRepository @Inject constructor(
    private val auth: FirebaseAuth, // Injected dependency
    private val firestore: FirebaseFirestore // Injected dependency
) {

    companion object {
        private const val TAG = "UserRepository"
        private const val USERS_COLLECTION = "users"
    }

    /**
     * Gets the current authenticated user from Firebase Auth.
     * @return FirebaseUser? The current authenticated user or null if not logged in
     */
    fun getCurrentUser(): FirebaseUser? {
        return auth.currentUser
    }

    /**
     * Checks if the current user is logged in.
     * @return Boolean True if a user is logged in, false otherwise
     */
    fun isUserLoggedIn(): Boolean {
        return getCurrentUser() != null
    }

    /**
     * Gets a reference to a user document in Firestore.
     * @param userId String The user's ID
     * @return DocumentReference The reference to the user document
     */
    fun getUserDocumentReference(userId: String) = firestore.collection(USERS_COLLECTION).document(userId)

    /**
     * Saves the user profile to Firestore.
     * @param user User The user profile to save
     * @return Boolean True if successful, false otherwise
     */
    suspend fun saveUserProfile(user: User): Boolean {
        // Get the current authenticated user first, return false if not logged in
        val currentUser = getCurrentUser() ?: return false

        try {
            // First check if the Firestore reference is valid
            if (currentUser.uid.isBlank()) {
                Log.e(TAG, "Invalid user ID")
                return false
            }

            // Create a map of values to update/set, excluding uid
            val userMap = mapOf(
                // Do NOT include "uid"
                "displayName" to user.displayName,
                "phoneNumber" to user.phoneNumber.ifBlank { currentUser.phoneNumber ?: "" },
                "email" to user.email,
                "bio" to user.bio,
                "photoUrl" to user.photoUrl,
                "lastActive" to com.google.firebase.firestore.FieldValue.serverTimestamp()
                // Add other updatable fields from the User object as needed
                // "allowTagging" to user.allowTagging,
                // "profileVisibility" to user.profileVisibility,
                // "notificationSettings" to user.notificationSettings.toMap(), // Convert nested objects to maps if needed
                // "themePreferences" to user.themePreferences.toMap() // Convert nested objects to maps if needed
            )

            // Get a reference to the user document
            val userDocRef = firestore.collection(USERS_COLLECTION).document(currentUser.uid)

            // Use update() for existing docs, set(merge) might also work but update is clearer for partial updates
            // If creating the doc here for the first time, ensure all necessary default fields are included in userMap
            val docSnapshot = userDocRef.get().await()
            if (docSnapshot.exists()) {
                 Log.d(TAG, "Updating existing user profile for UID: ${currentUser.uid}")
                userDocRef.update(userMap).await()
            } else {
                // This case should ideally be handled by createUserIfNotExists,
                // but if we need to create here, use set() with the map.
                Log.w(TAG, "User document does not exist, creating profile with set() for UID: ${currentUser.uid}")
                // Ensure all necessary fields are present if creating via saveUserProfile
                // Consider merging with default values map if this path is possible
                userDocRef.set(userMap).await()
            }

            Log.d(TAG, "User profile saved successfully: ${user.displayName}")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error saving user profile for UID: ${currentUser.uid}. Error: ${e.message}", e)
            return false
        }
    }

    /**
     * Validates if the locally authenticated user still exists in Firestore.
     * If the user doesn't exist in Firestore but is logged in locally, signs them out.
     * @return Boolean True if the user is authenticated and exists in Firestore, false otherwise
     */
    @Suppress("unused") // Suppressing warning as it might be used later or for specific validation scenarios
    suspend fun validateUserAuthentication(): Boolean {
        val currentUser = getCurrentUser() ?: return false

        try {
            // First, try to revalidate the auth token
            val tokenValid = FirebaseHelper.revalidateAuthToken()
            if (!tokenValid) {
                Log.w(TAG, "Auth token validation failed for user ${currentUser.uid}")
                return false
            }

            // Then check if user document exists in Firestore
            val userDoc = firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .get()
                .await()

            if (!userDoc.exists()) {
                Log.w(TAG, "User ${currentUser.uid} exists in Auth but not in Firestore. Signing out.")
                // Reset theme settings to defaults when signing out
                app.donskey.cantdecide.ui.theme.ThemeManager.resetToDefaults()
                // User doesn't exist in Firestore, sign them out
                auth.signOut()
                return false
            }

            // User exists in both Auth and Firestore
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error validating user authentication: ${e.message}", e)
            // On error, return true to maintain current behavior
            return true
        }
    }

    /**
     * Creates a new user in Firestore if the user doesn't already exist.
     * @param phoneNumber String The user's phone number
     * @return Boolean True if successful, false otherwise
     */
    suspend fun createUserIfNotExists(phoneNumber: String): Boolean {
        val currentUser = getCurrentUser() ?: return false

        try {
            // Check if user document already exists
            val userDocRef = firestore.collection(USERS_COLLECTION).document(currentUser.uid)
            val userDoc = userDocRef.get().await()

            if (!userDoc.exists()) {
                // Create a map for the new user document, excluding uid
                val newUserMap = mapOf(
                    "phoneNumber" to phoneNumber,
                    "displayName" to "", // Initialize other fields with defaults
                    "email" to "",
                    "photoUrl" to "",
                    "bio" to "",
                    "allowTagging" to true,
                    "profileVisibility" to "public",
                    "hideOnlineStatus" to false,
                    "publicProfile" to false,
                    "fcmToken" to "",
                    "createdAt" to com.google.firebase.firestore.FieldValue.serverTimestamp(),
                    "lastActive" to com.google.firebase.firestore.FieldValue.serverTimestamp(),
                    "pollsCreated" to 0,
                    "pollsVotedOn" to 0,
                    "votesReceived" to 0,
                    "decisionsCount" to 0,
                    "friendsCount" to 0,
                    "pendingRequestCount" to 0,
                    "notificationSettings" to app.donskey.cantdecide.data.models.NotificationSettings(), // Default settings
                    "themePreferences" to app.donskey.cantdecide.data.models.ThemePreferences() // Default preferences
                    // Do NOT include "uid" field here
                )

                Log.d(TAG, "Creating new user document for UID: ${currentUser.uid} with phone: $phoneNumber")

                // Save the map to Firestore using the user's UID as the document ID
                userDocRef.set(newUserMap).await()

                Log.d(TAG, "User document created successfully for UID: ${currentUser.uid}")
                return true
            }

            // User already exists
            Log.d(TAG, "User document already exists for UID: ${currentUser.uid}")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error creating user document: ${e.message}", e)
            return false
        }
    }

    /**
     * Gets the user profile data from Firestore.
     * @return User? The user profile or null if not found or error
     */
    suspend fun getUserProfile(): User? {
        val currentUser = getCurrentUser()
        // Log whether a current user exists in FirebaseAuth
        if (currentUser == null) {
            Log.w(TAG, "getUserProfile: No authenticated user found in FirebaseAuth.")
            return null
        }

        // Log the UID being used for the Firestore query
        Log.d(TAG, "getUserProfile: Attempting to fetch profile for UID: ${currentUser.uid}")

        return try {
            val userDoc = firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .get()
                .await()

            // Log whether the document exists in Firestore
            if (userDoc.exists()) {
                Log.d(TAG, "getUserProfile: Document found for UID: ${currentUser.uid}")
                // Attempt to convert the document to a User object
                val userObject = userDoc.toObject(User::class.java)
                if (userObject != null) {
                    Log.d(TAG, "getUserProfile: Successfully converted document to User object. DisplayName: ${userObject.displayName}")
                    userObject // Return the user object
                } else {
                    // Log if conversion fails
                    Log.e(TAG, "getUserProfile: Failed to convert Firestore document to User object for UID: ${currentUser.uid}")
                    null // Return null if conversion fails
                }
            } else {
                // Log if document doesn't exist
                Log.w(TAG, "getUserProfile: Firestore document does not exist for UID: ${currentUser.uid}")
                null // Return null if document doesn't exist
            }
        } catch (e: Exception) {
            // Log any exceptions during the fetch
            Log.e(TAG, "getUserProfile: Error retrieving user profile for UID: ${currentUser.uid}. Error: ${e.message}", e)
            null // Return null on error
        }
    }

    /**
     * Checks if the current user has completed their profile setup.
     * A profile is considered complete if the user has a display name.
     * @return Boolean True if profile is complete, false otherwise
     */
    suspend fun hasCompletedProfile(): Boolean {
        val user = getUserProfile() ?: return false
        return user.displayName.isNotBlank()
    }

    /**
     * Updates the user's last active timestamp.
     */
    @Suppress("unused") // Suppressing warning as this might be automatically called by a Cloud Function or used later
    suspend fun updateLastActive() {
        val currentUser = getCurrentUser() ?: return

        try {
            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .update("lastActive", com.google.firebase.Timestamp.now())
                .await()

            Log.d(TAG, "Updated last active timestamp")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating last active timestamp: ${e.message}", e)
        }
    }

    /**
     * Updates the notification settings for the current user.
     * @param notificationSettings Map<String, Boolean> The notification settings to update
     * @return Boolean True if successful, false otherwise
     */
    suspend fun updateNotificationSettings(notificationSettings: Map<String, Boolean>): Boolean {
        val currentUser = getCurrentUser() ?: return false

        try {
            // Create a map with nested field path
            val updateData = HashMap<String, Any>()

            // Iterate through the notification settings and create dot notation paths
            for ((key, value) in notificationSettings) {
                updateData["notificationSettings.$key"] = value
            }

            // Update the user document with the new notification settings
            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .update(updateData)
                .await()

            Log.d(TAG, "Updated notification settings successfully")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating notification settings: ${e.message}", e)
            return false
        }
    }

    /**
     * Updates the privacy settings for the current user.
     * @param allowTagging Boolean Whether to allow tagging
     * @param profileVisibility String Public or private profile visibility setting
     * @param hideOnlineStatus Boolean Whether to hide user's online status
     * @param publicProfile Boolean Whether to make the profile public
     * @return Boolean True if successful, false otherwise
     */
    suspend fun updatePrivacySettings(
        allowTagging: Boolean,
        profileVisibility: String,
        hideOnlineStatus: Boolean,
        publicProfile: Boolean
    ): Boolean {
        val currentUser = getCurrentUser() ?: return false

        try {
            // Create a map with the privacy settings fields
            val updateData = mapOf(
                "allowTagging" to allowTagging,
                "profileVisibility" to profileVisibility,
                "hideOnlineStatus" to hideOnlineStatus,
                "publicProfile" to publicProfile
            )

            // Update the user document with the new privacy settings
            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .update(updateData)
                .await()

            Log.d(TAG, "Updated privacy settings successfully")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating privacy settings: ${e.message}", e)
            return false
        }
    }

    /**
     * Updates the theme preferences for the current user.
     * @param darkMode Boolean Whether dark mode is enabled
     * @param themeColorIndex Int The index of the selected theme color
     * @return Boolean True if successful, false otherwise
     */
    suspend fun updateThemePreferences(
        darkMode: Boolean,
        themeColorIndex: Int
    ): Boolean {
        val currentUser = getCurrentUser() ?: return false

        try {
            // Create a map with nested field path
            val updateData = mapOf(
                "themePreferences.darkMode" to darkMode,
                "themePreferences.themeColorIndex" to themeColorIndex
            )

            // Update the user document with the new theme preferences
            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .update(updateData)
                .await()

            Log.d(TAG, "Updated theme preferences successfully")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating theme preferences: ${e.message}", e)
            return false
        }
    }

    /**
     * Deletes all user data from Firestore.
     * @return Boolean True if successful, false otherwise
     */
    suspend fun deleteUserAccount(): Boolean {
        val currentUser = getCurrentUser() ?: return false

        try {
            // Get user document reference
            val userDocRef = firestore.collection(USERS_COLLECTION).document(currentUser.uid)

            // Delete the user document from Firestore
            userDocRef.delete().await()

            Log.d(TAG, "User document deleted from Firestore successfully for UID: ${currentUser.uid}")
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting user document from Firestore: ${e.message}", e)
            return false
        }
    }

    /**
     * Searches for a user by their phone number.
     * Note: This requires a Firestore index on the 'phoneNumber' field.
     * @param phoneNumber The E.164 formatted phone number to search for.
     * @return User? The found user or null if not found or error.
     */
    suspend fun findUserByPhoneNumber(phoneNumber: String): User? {
        Log.e(TAG, "=== DATABASE SEARCH DEBUG ===")
        Log.e(TAG, "Searching for user with phone number: $phoneNumber")

        // Ensure we don't search for the current user's own number accidentally
        val currentUser = getCurrentUser()
        Log.e(TAG, "Current user phone: ${currentUser?.phoneNumber}")

        if (currentUser?.phoneNumber == phoneNumber) {
             Log.e(TAG, "Attempted to search for current user's own phone number.")
            return null // Or return the current user if that's desired behavior
        }

        return try {
            Log.e(TAG, "Executing Firestore query...")
            val querySnapshot = firestore.collection(USERS_COLLECTION)
                .whereEqualTo("phoneNumber", phoneNumber)
                .limit(1) // Expecting only one user per phone number
                .get()
                .await()

            Log.e(TAG, "Query completed. Documents found: ${querySnapshot.size()}")

            if (!querySnapshot.isEmpty) {
                // Found a user
                val document = querySnapshot.documents[0]
                Log.e(TAG, "Document data: ${document.data}")
                val user = document.toObject(User::class.java)
                if (user != null) {
                    Log.e(TAG, "User found: ${user.uid} - ${user.displayName} - ${user.phoneNumber}")
                    user // Return the found user
                } else {
                    Log.e(TAG, "Failed to convert found user document to User object for phone: $phoneNumber")
                    null
                }
            } else {
                // No user found with that phone number
                Log.e(TAG, "No user found with phone number: $phoneNumber")

                // Let's also check what users exist in the database for debugging
                Log.e(TAG, "Checking all users in database for debugging...")
                val allUsersSnapshot = firestore.collection(USERS_COLLECTION)
                    .limit(10)
                    .get()
                    .await()

                Log.e(TAG, "Total users in database: ${allUsersSnapshot.size()}")
                allUsersSnapshot.documents.forEach { doc ->
                    val userData = doc.data
                    Log.e(TAG, "User: ${doc.id} - Phone: ${userData?.get("phoneNumber")} - Name: ${userData?.get("displayName")}")
                }

                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error searching for user by phone number ($phoneNumber): ${e.message}", e)
            null // Return null on error
        }
    }

    /**
     * Fetches user profiles for a given list of user IDs.
     * Uses Firestore\'s whereIn query for efficiency.
     * Note: Firestore \'whereIn\' queries have limitations on the number of items (e.g., 30).
     * This function does not currently handle chunking for lists larger than the limit.
     *
     * @param userIds List<String> The list of user IDs to fetch profiles for.
     * @return Map<String, User> A map where keys are user IDs and values are the corresponding User objects. Returns an empty map if input list is empty or on error.
     */
    suspend fun getUserProfilesMap(userIds: List<String>): Map<String, User> {
        // Return early if the list is empty
        if (userIds.isEmpty()) {
            Log.d(TAG, "getUserProfilesMap: Received empty list of user IDs.")
            return emptyMap()
        }

        // Log the IDs being requested
        Log.d(TAG, "getUserProfilesMap: Attempting to fetch profiles for IDs: $userIds")

        return try {
            // Fetch documents where the document ID is in the provided list
            // Note: Using FieldPath.documentId() to query by document ID directly.
            val querySnapshot = firestore.collection(USERS_COLLECTION)
                .whereIn(FieldPath.documentId(), userIds)
                .get()
                .await()

            // Process the results
            val profilesMap = mutableMapOf<String, User>()
            for (document in querySnapshot.documents) {
                // Attempt to convert the document to a User object
                val user = document.toObject(User::class.java)
                if (user != null) {
                    // Use document.id as the key (which is the user's UID)
                    profilesMap[document.id] = user
                    Log.d(TAG, "getUserProfilesMap: Successfully fetched and mapped profile for ID: ${document.id}")
                } else {
                    Log.w(TAG, "getUserProfilesMap: Failed to convert document to User object for ID: ${document.id}")
                }
            }

            Log.d(TAG, "getUserProfilesMap: Successfully fetched ${profilesMap.size} profiles out of ${userIds.size} requested.")
            profilesMap // Return the map of successfully fetched profiles

        } catch (e: Exception) {
            // Log any errors during the fetch
            Log.e(TAG, "getUserProfilesMap: Error fetching user profiles for IDs: $userIds. Error: ${e.message}", e)
            emptyMap() // Return an empty map on error
        }
    }

    /**
     * Updates the FCM token for a given user.
     *
     * @param userId The ID of the user whose token is to be updated.
     * @param token The new FCM token.
     * @return Boolean True if the update was successful, false otherwise.
     */
    suspend fun updateFcmToken(userId: String, token: String): Boolean {
        if (userId.isBlank()) {
            Log.e(TAG, "updateFcmToken: User ID is blank.")
            return false
        }
        return try {
            firestore.collection(USERS_COLLECTION).document(userId)
                .update("fcmToken", token)
                .await()
            Log.d(TAG, "FCM token updated successfully for user: $userId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating FCM token for user $userId: ${e.message}", e)
            false
        }
    }

    /**
     * Manually refreshes and saves the current FCM token for the logged-in user.
     * Useful for testing or ensuring the token is up to date.
     */
    suspend fun refreshFcmToken(): Boolean {
        val currentUser = getCurrentUser()
        if (currentUser == null) {
            Log.e(TAG, "refreshFcmToken: No user logged in")
            return false
        }

        return try {
            val fcmToken = com.google.firebase.messaging.FirebaseMessaging.getInstance().token.await()
            if (fcmToken.isNotBlank()) {
                val success = updateFcmToken(currentUser.uid, fcmToken)
                Log.d(TAG, "FCM token refresh ${if (success) "successful" else "failed"} for user: ${currentUser.uid}")
                success
            } else {
                Log.w(TAG, "refreshFcmToken: FCM token is blank")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing FCM token: ${e.message}", e)
            false
        }
    }

    /**
     * Observes the current user's profile data from Firestore in real-time.
     * @return Flow<User?> A flow that emits the User object when it changes, or null if not found/error.
     */
    fun observeUserProfile(): Flow<User?> {
        val currentUser = getCurrentUser()
        if (currentUser == null) {
            Log.w(TAG, "observeUserProfile: No authenticated user found in FirebaseAuth.")
            return kotlinx.coroutines.flow.flowOf(null) // Emit null immediately if no user
        }

        Log.d(TAG, "observeUserProfile: Setting up real-time listener for UID: ${currentUser.uid}")

        return callbackFlow {
            val userDocRef = firestore.collection(USERS_COLLECTION).document(currentUser.uid)

            // Explicitly type the listener and its parameters
            val listener = EventListener<DocumentSnapshot> { snapshot: DocumentSnapshot?, error: FirebaseFirestoreException? ->
                if (error != null) {
                    Log.e(TAG, "observeUserProfile: Listener failed for UID: ${currentUser.uid}", error)
                    trySend(null).isSuccess // Offer null on error
                    close(error) // Close the flow on error
                    return@EventListener
                }

                if (snapshot != null && snapshot.exists()) {
                    Log.d(TAG, "observeUserProfile: Data received for UID: ${currentUser.uid}")
                    val user = snapshot.toObject(User::class.java)
                    if (user != null) {
                        // Corrected log to use existing fields, assuming followingCount was a mistake
                        Log.d(TAG, "observeUserProfile: Successfully converted to User. DisplayName: ${user.displayName}, Friends: ${user.friendsCount}")
                        trySend(user).isSuccess
                    } else {
                        Log.e(TAG, "observeUserProfile: Failed to convert document to User for UID: ${currentUser.uid}")
                        trySend(null).isSuccess
                    }
                } else {
                    Log.w(TAG, "observeUserProfile: Document does not exist for UID: ${currentUser.uid} (or is null)")
                    trySend(null).isSuccess // Offer null if document doesn't exist
                }
            }

            val registration = userDocRef.addSnapshotListener(listener)

            // This will be called when the flow collector is cancelled
            awaitClose {
                Log.d(TAG, "observeUserProfile: Listener removed for UID: ${currentUser.uid}")
                registration.remove()
            }
        }
    }

    /**
     * Gets the user profile data from Firestore for a specific user ID.
     * @param userId String The ID of the user whose profile is to be fetched.
     * @return User? The user profile or null if not found or error.
     */
    suspend fun getUserProfileById(userId: String): User? {
        if (userId.isBlank()) {
            Log.w(TAG, "getUserProfileById: Provided userId is blank.")
            return null
        }

        Log.d(TAG, "getUserProfileById: Attempting to fetch profile for UID: $userId")

        return try {
            val userDoc = firestore.collection(USERS_COLLECTION)
                .document(userId)
                .get()
                .await()

            if (userDoc.exists()) {
                Log.d(TAG, "getUserProfileById: Document found for UID: $userId")
                val userObject = userDoc.toObject(User::class.java)
                if (userObject != null) {
                    Log.d(TAG, "getUserProfileById: Successfully converted document to User object. DisplayName: ${userObject.displayName}")
                    userObject
                } else {
                    Log.e(TAG, "getUserProfileById: Failed to convert Firestore document to User object for UID: $userId")
                    null
                }
            } else {
                Log.w(TAG, "getUserProfileById: Firestore document does not exist for UID: $userId")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "getUserProfileById: Error fetching user profile for UID: $userId. Error: ${e.message}", e)
            null
        }
    }
}