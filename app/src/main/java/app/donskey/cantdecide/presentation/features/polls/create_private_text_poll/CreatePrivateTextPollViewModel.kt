package app.donskey.cantdecide.presentation.features.polls.create_private_text_poll

import androidx.lifecycle.viewModelScope
import app.donskey.cantdecide.data.model.PollOption
import app.donskey.cantdecide.data.model.PrivateTextPoll
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.domain.repository.PollsRepository
import app.donskey.cantdecide.presentation.base.BaseViewModel
import com.google.firebase.Timestamp
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject
import app.donskey.cantdecide.data.repositories.FriendsRepository
import app.donskey.cantdecide.data.models.User
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.ExperimentalCoroutinesApi
import app.donskey.cantdecide.util.Resource

/**
 * ViewModel for the Create Private Text Poll screen.
 * Manages the UI state and handles business logic related to creating a private text poll.
 */
@HiltViewModel
class CreatePrivateTextPollViewModel @Inject constructor(
    private val pollsRepository: PollsRepository,
    private val userRepository: UserRepository,
    private val friendsRepository: FriendsRepository
) : BaseViewModel<CreatePrivateTextPollContract.Event, CreatePrivateTextPollContract.State, CreatePrivateTextPollContract.Effect>() {

    private val minOptions: Int = CreatePrivateTextPollDefaults.MIN_POLL_OPTIONS
    private val maxOptions: Int = CreatePrivateTextPollDefaults.MAX_POLL_OPTIONS

    /**
     * Creates the initial state for the screen.
     */
    override fun createInitialState(): CreatePrivateTextPollContract.State {
        return CreatePrivateTextPollContract.State(
            numberOfOptions = minOptions,
            pollOptions = List(minOptions) { "" },
            pollOptionErrors = List(minOptions) { null },
            friendsList = emptyList()
        )
    }

    init {
        setState { createInitialState() }
        fetchFriends()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun fetchFriends() {
        viewModelScope.launch {
            setState { copy(isLoading = true) }
            val currentUserId = userRepository.getCurrentUser()?.uid
            if (currentUserId == null) {
                Log.e("CreatePrivateTextPollVM", "Current user ID is null. Cannot fetch friends.")
                setState { copy(isLoading = false, friendSelectionError = "Could not load friends: User not found.") }
                return@launch
            }

            friendsRepository.getCurrentUserFriends()
                .mapLatest { friendList ->
                    friendList.map { friend ->
                        async(Dispatchers.IO) {
                            val friendUserId = friend.getOtherUserId(currentUserId)
                            try {
                                val userDoc = userRepository.getUserDocumentReference(friendUserId).get().await()
                                val userProfile = userDoc.toObject(User::class.java)
                                if (userProfile != null) {
                                    CreatePrivateTextPollContract.FriendListItem(
                                        id = userProfile.uid,
                                        name = userProfile.displayName.ifBlank { "Friend" },
                                        photoUrl = userProfile.photoUrl.ifEmpty { null }
                                    )
                                } else {
                                    Log.w("CreatePrivateTextPollVM", "User profile not found for friend ID: $friendUserId")
                                    null
                                }
                            } catch (e: Exception) {
                                Log.e("CreatePrivateTextPollVM", "Error fetching profile for friend ID: $friendUserId", e)
                                null
                            }
                        }
                    }.awaitAll().filterNotNull()
                }
                .collect { friendListItems ->
                    Log.d("CreatePrivateTextPollVM", "Fetched friends: ${friendListItems.size}")
                    // Sort friends alphabetically by name
                    val sortedFriendListItems = friendListItems.sortedWith(
                        compareBy(String.CASE_INSENSITIVE_ORDER) { it.name }
                    )
                    setState { copy(friendsList = sortedFriendListItems, isLoading = false, friendSelectionError = if (sortedFriendListItems.isEmpty()) "No friends to invite." else null) }
                }
        }
    }

    /**
     * Handles incoming events from the UI.
     * @param event The event to handle.
     */
    override fun handleEvent(event: CreatePrivateTextPollContract.Event) {
        viewModelScope.launch {
            when (event) {
                is CreatePrivateTextPollContract.Event.OnPollQuestionChanged -> {
                    setState { copy(pollQuestion = event.question, pollQuestionError = null) }
                }
                is CreatePrivateTextPollContract.Event.OnNumberOfOptionsChanged -> {
                    val newCount: Int = event.count.coerceIn(minOptions, maxOptions)
                    val currentOptions: List<String> = uiState.value.pollOptions
                    val newOptions: List<String> = if (newCount > currentOptions.size) {
                        currentOptions + List(newCount - currentOptions.size) { "" }
                    } else {
                        currentOptions.take(newCount)
                    }
                    val newOptionErrors: List<String?> = List(newCount) { null }
                    setState { copy(numberOfOptions = newCount, pollOptions = newOptions, pollOptionErrors = newOptionErrors) }
                }
                is CreatePrivateTextPollContract.Event.OnPollOptionChanged -> {
                    val updatedOptions: MutableList<String> = uiState.value.pollOptions.toMutableList()
                    val updatedErrors: MutableList<String?> = uiState.value.pollOptionErrors.toMutableList()
                    if (event.index >= 0 && event.index < updatedOptions.size) {
                        updatedOptions[event.index] = event.text
                        updatedErrors[event.index] = null
                        setState { copy(pollOptions = updatedOptions, pollOptionErrors = updatedErrors) }
                    }
                }
                is CreatePrivateTextPollContract.Event.OnCreatePollClicked -> {
                    setState { copy(isLoading = true, friendSelectionError = null, pollQuestionError = null, pollOptionErrors = List(uiState.value.numberOfOptions) {null} ) }
                    viewModelScope.launch {
                        validateAndCreatePoll()
                    }
                }
                is CreatePrivateTextPollContract.Event.OnExpiryDateChanged -> {
                    setState { copy(expiryDate = event.dateMillis, showDatePickerDialog = false) }
                }
                is CreatePrivateTextPollContract.Event.OnToggleDatePickerDialog -> {
                    setState { copy(showDatePickerDialog = !uiState.value.showDatePickerDialog) }
                }
                is CreatePrivateTextPollContract.Event.ClearFocusAfterValidationFlag -> {
                    setState { copy(needsToFocusAfterValidation = false) }
                }
                is CreatePrivateTextPollContract.Event.OnClearExpiryIconClicked -> {
                    setState { copy(showClearExpiryConfirmDialog = true) }
                }
                is CreatePrivateTextPollContract.Event.OnToggleClearExpiryConfirmDialog -> {
                    setState { copy(showClearExpiryConfirmDialog = !uiState.value.showClearExpiryConfirmDialog) }
                }
                is CreatePrivateTextPollContract.Event.OnConfirmClearExpiryDate -> {
                    setState { copy(expiryDate = null, showClearExpiryConfirmDialog = false) }
                }
                is CreatePrivateTextPollContract.Event.OnPollAnonymityChanged -> {
                    val newIsAnonymous = event.anonymous
                    val updatedFriendsList = if (newIsAnonymous) {
                        uiState.value.friendsList.map { it.copy(isSelected = false) }
                    } else {
                        uiState.value.friendsList
                    }
                    setState { copy(anonymous = newIsAnonymous, friendSelectionError = null, friendsList = updatedFriendsList) }
                }
                is CreatePrivateTextPollContract.Event.OnFriendSelected -> {
                    val updatedList = uiState.value.friendsList.map {
                        if (it.id == event.friendId) it.copy(isSelected = event.isSelected) else it
                    }
                    setState { copy(friendsList = updatedList, friendSelectionError = null) }
                }
                is CreatePrivateTextPollContract.Event.OnSelectAllFriendsClicked -> {
                    val updatedList = uiState.value.friendsList.map { it.copy(isSelected = event.selectAll) }
                    setState { copy(friendsList = updatedList, friendSelectionError = null) }
                }
                is CreatePrivateTextPollContract.Event.ClearFocusFriendErrorFlag -> {
                    setState { copy(needsToFocusFriendError = false) }
                }
            }
        }
    }

    private suspend fun validateAndCreatePoll() {
        val stateToValidate = uiState.value
        var isValid = true
        var focusFriendError = false

        val determinedPollQuestionError: String? = if (stateToValidate.pollQuestion.isBlank()) {
            isValid = false
            "Poll Question cannot be empty"
        } else { null }

        val determinedOptionErrors: List<String?> = stateToValidate.pollOptions.mapIndexed { index, option ->
            if (option.isBlank()) {
                isValid = false; "Option ${index + 1} cannot be empty"
            } else { null }
        }

        var determinedFriendSelectionError: String? = null
        if (!stateToValidate.anonymous) {
            if (!stateToValidate.friendsList.any { it.isSelected }) {
                isValid = false
                determinedFriendSelectionError = "Please select at least one friend to vote."
                focusFriendError = true
            }
        }

        if (!isValid) {
            setState {
                copy(
                    isLoading = false,
                    pollQuestionError = determinedPollQuestionError,
                    pollOptionErrors = determinedOptionErrors,
                    friendSelectionError = determinedFriendSelectionError,
                    needsToFocusAfterValidation = determinedPollQuestionError != null || determinedOptionErrors.any { it != null },
                    needsToFocusFriendError = focusFriendError
                )
            }
        } else {
            Log.d("CreatePrivateTextPollVM", "Validation passed, attempting to fetch user and create poll.")
            val currentUser = userRepository.getCurrentUser()
            val userProfile = userRepository.getUserProfile()

            if (currentUser == null || userProfile == null) {
                setEffect { CreatePrivateTextPollContract.Effect.ShowError("Error: Could not retrieve user information.") }
                setState { copy(isLoading = false) }
                return
            }

            val pollOptions = stateToValidate.pollOptions.map {
                PollOption(optionId = UUID.randomUUID().toString(), text = it)
            }

            val allowedVoters = if (!stateToValidate.anonymous) {
                stateToValidate.friendsList.filter { it.isSelected }.map { it.id }
            } else {
                stateToValidate.friendsList.map { it.id }
            }

            val textPoll = PrivateTextPoll(
                creatorId = currentUser.uid,
                creatorName = userProfile.displayName.ifBlank { "A User" },
                question = stateToValidate.pollQuestion,
                options = pollOptions,
                allowedVoters = allowedVoters,
                anonymous = stateToValidate.anonymous,
                createdAt = Timestamp.now(),
                closesAt = stateToValidate.expiryDate?.let { Timestamp(it / 1000, ((it % 1000) * 1_000_000).toInt()) },
                status = "ACTIVE"
            )

            Log.d("CreatePrivateTextPollVM", "Attempting to create poll: $textPoll")

            when (val resourceResult = pollsRepository.createTextPoll(textPoll)) {
                is Resource.Success -> {
                    Log.d("CreatePrivateTextPollVM", "Poll created successfully! Poll ID: ${resourceResult.data}")
                    setEffect { CreatePrivateTextPollContract.Effect.ShowError("Poll created successfully!") }
                    setEffect { CreatePrivateTextPollContract.Effect.NavigateBack }
                    val currentFriends = uiState.value.friendsList
                    setState { createInitialState().copy(isLoading = false, friendsList = currentFriends) }
                }
                is Resource.Error -> {
                    Log.e("CreatePrivateTextPollVM", "Failed to create poll: ${resourceResult.message}")
                    setEffect { CreatePrivateTextPollContract.Effect.ShowError("Failed to create poll: ${resourceResult.message ?: "Unknown error"}") }
                    setState { copy(isLoading = false) }
                }
                is Resource.Loading -> {
                    Log.d("CreatePrivateTextPollVM", "Poll creation is loading...")
                }
            }
        }
    }
}