package app.donskey.cantdecide.ui.poll_details

// Specific imports to avoid conflicts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import app.donskey.cantdecide.R
import app.donskey.cantdecide.data.model.PollOption
import app.donskey.cantdecide.data.model.PollVote
import app.donskey.cantdecide.data.model.PrivateTextPoll
import coil.compose.AsyncImage
import com.google.firebase.Timestamp
import java.util.Date

/**
 * Composable function for the Poll Details screen.
 * This screen allows users to view the details of a poll, select an option, and submit their vote.
 * It also displays the user's current vote if they have already voted.
 *
 * @param viewModel The [PollDetailsViewModel] instance associated with this screen, injected by Hilt.
 * @param onNavigateBack Callback invoked when the user initiates a back navigation (e.g., presses the back button in the app bar).
 */
@Composable
fun PollDetailsScreen(
    viewModel: PollDetailsViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    LaunchedEffect(Unit) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is PollDetailsEffect.VoteSubmittedSuccessfully -> {
                    snackbarHostState.showSnackbar(
                        message = "Vote submitted successfully! Results updated.",
                        duration = SnackbarDuration.Short
                    )
                }
                is PollDetailsEffect.ShowError -> {
                    snackbarHostState.showSnackbar(
                        message = effect.message,
                        duration = SnackbarDuration.Short
                    )
                }
                is PollDetailsEffect.NavigateBackPollDeleted -> {
                    // Show a brief message before navigating back
                    snackbarHostState.showSnackbar(
                        message = "Poll has been deleted",
                        duration = SnackbarDuration.Short
                    )
                    // Navigate back to the previous screen
                    onNavigateBack()
                }
                is PollDetailsEffect.FriendAddedSuccessfully -> {
                    snackbarHostState.showSnackbar(
                        message = "${effect.friendName} has been invited to the poll!",
                        duration = SnackbarDuration.Short
                    )
                }
            }
        }
    }

    PollDetailsScreenContent(
        uiState = uiState,
        snackbarHostState = snackbarHostState,
        onNavigateBack = onNavigateBack,
        onOptionSelected = { optionId -> viewModel.handleEvent(PollDetailsEvent.OptionSelected(optionId)) },
        onSubmitVote = { viewModel.handleEvent(PollDetailsEvent.SubmitVoteClicked) },
        onInviteMoreFriends = { viewModel.handleEvent(PollDetailsEvent.InviteMoreFriendsClicked) },
        onDismissAddFriendDialog = { viewModel.handleEvent(PollDetailsEvent.DismissAddFriendDialog) },
        onAddFriendToPoll = { friendId -> viewModel.handleEvent(PollDetailsEvent.AddFriendToPoll(friendId)) }
    )
}

@Preview(showBackground = true, name = "Poll Details - Loading")
@Composable
fun PollDetailsScreenLoadingPreview() {
    MaterialTheme {
        PollDetailsScreenContent(
            uiState = PollDetailsUiState(isLoading = true, poll = null),
            snackbarHostState = SnackbarHostState(),
            onNavigateBack = {},
            onOptionSelected = {},
            onSubmitVote = {}
        )
    }
}

@Preview(showBackground = true, name = "Poll Details - Loaded, Not Voted")
@Composable
fun PollDetailsScreenLoadedNotVotedPreview() {
    val samplePoll = PrivateTextPoll(
        pollId = "poll123",
        question = "What is your favorite color?",
        options = listOf(
            PollOption("opt1", "Red"),
            PollOption("opt2", "Green"),
            PollOption("opt3", "Blue")
        ),
        creatorName = "Test User",
        createdAt = Timestamp(Date())
    )
    MaterialTheme {
         PollDetailsScreenContent(
            uiState = PollDetailsUiState(isLoading = false, poll = samplePoll, selectedOptionId = "opt2"),
            snackbarHostState = SnackbarHostState(),
            onNavigateBack = {},
            onOptionSelected = {},
            onSubmitVote = {}
        )
    }
}

@Preview(showBackground = true, name = "Poll Details - Already Voted")
@Composable
fun PollDetailsScreenAlreadyVotedPreview() {
    val samplePoll = PrivateTextPoll(
        pollId = "poll123",
        question = "What is your favorite color?",
        options = listOf(
            PollOption("opt1", "Red"),
            PollOption("opt2", "Green"),
            PollOption("opt3", "Blue")
        ),
        creatorName = "Test User",
        createdAt = Timestamp(Date())
    )
    val sampleVote = PollVote(pollId = "poll123", voterId = "userABC", optionId = "opt1", votedAt = Timestamp(Date()))
    MaterialTheme {
        PollDetailsScreenContent(
            uiState = PollDetailsUiState(isLoading = false, poll = samplePoll, userVote = sampleVote),
            snackbarHostState = SnackbarHostState(),
            onNavigateBack = {},
            onOptionSelected = {},
            onSubmitVote = {}
        )
    }
}

@Preview(showBackground = true, name = "Poll Details - Error Loading Poll")
@Composable
fun PollDetailsScreenErrorPreview() {
    MaterialTheme {
        PollDetailsScreenContent(
            uiState = PollDetailsUiState(isLoading = false, error = "Failed to load poll details. Please try again later."),
            snackbarHostState = SnackbarHostState(),
            onNavigateBack = {},
            onOptionSelected = {},
            onSubmitVote = {}
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PollDetailsScreenContent(
    uiState: PollDetailsUiState,
    snackbarHostState: SnackbarHostState,
    onNavigateBack: () -> Unit,
    onOptionSelected: (String) -> Unit,
    onSubmitVote: () -> Unit,
    onInviteMoreFriends: () -> Unit = {},
    onDismissAddFriendDialog: () -> Unit = {},
    onAddFriendToPoll: (String) -> Unit = {}
) {
    val poll = uiState.poll
    val currentUserId = uiState.currentUserId

     Scaffold(
        snackbarHost = { SnackbarHost(hostState = snackbarHostState) },
        topBar = {
            CenterAlignedTopAppBar(
                title = { Text("Poll Options") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Navigate back"
                        )
                    }
                },
                 colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading && poll == null -> {
                    Column(
                        modifier = Modifier.fillMaxSize().padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        CircularProgressIndicator()
                        Text(text = "Loading poll details...", modifier = Modifier.padding(top = 8.dp))
                    }
                }
                uiState.error != null && poll == null -> {
                    Column(
                        modifier = Modifier.fillMaxSize().padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(text = uiState.error, color = MaterialTheme.colorScheme.error, textAlign = TextAlign.Center)
                    }
                }
                poll != null -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 20.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {
                        LazyColumn(
                            modifier = Modifier.fillMaxWidth().padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            item {
                                Text(
                                    text = poll.question,
                                    style = MaterialTheme.typography.headlineSmall,
                                    modifier = Modifier.padding(bottom = 16.dp)
                                )
                            }

                            val showResults = uiState.userVote != null ||
                                              (poll.status != "ACTIVE") ||
                                              (poll.closesAt != null && poll.closesAt.toDate().before(Date()))

                            if (showResults) {
                                items(uiState.optionResults) { result ->
                                    PollResultItem(result = result)
                                }
                                item {
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = "Total votes: ${poll.voteCount}",
                                        style = MaterialTheme.typography.bodyMedium,
                                        modifier = Modifier.align(Alignment.End)
                                    )
                                }
                                uiState.userVote?.let { vote ->
                                    val votedOptionText = poll.options.find { it.optionId == vote.optionId }?.text ?: "an option"
                                    item {
                                        Text(
                                            text = "You voted for: $votedOptionText",
                                            style = MaterialTheme.typography.bodyLarge,
                                            fontWeight = FontWeight.Bold,
                                            modifier = Modifier.padding(top = 16.dp)
                                        )
                                    }
                                }
                            } else {
                                items(poll.options) { option ->
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .selectable(
                                                selected = (option.optionId == uiState.selectedOptionId),
                                                onClick = {
                                                    onOptionSelected(option.optionId)
                                                },
                                                role = Role.RadioButton,
                                                enabled = true
                                            )
                                            .padding(vertical = 12.dp),
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        RadioButton(
                                            selected = (option.optionId == uiState.selectedOptionId),
                                            onClick = null,
                                            enabled = true
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(text = option.text, style = MaterialTheme.typography.bodyLarge)
                                    }
                                    HorizontalDivider()
                                }

                                item {
                                    Button(
                                        onClick = onSubmitVote,
                                        enabled = uiState.selectedOptionId != null && !uiState.isSubmittingVote,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(top = 16.dp)
                                    ) {
                                        if (uiState.isSubmittingVote) {
                                            CircularProgressIndicator(modifier = Modifier.size(24.dp), color = MaterialTheme.colorScheme.onPrimary)
                                        } else {
                                            Text("Submit Vote")
                                        }
                                    }
                                }
                            }

                            if (currentUserId == poll.creatorId && !poll.anonymous && uiState.invitedFriendsVoteStatus.isNotEmpty()) {
                                item {
                                    Spacer(modifier = Modifier.height(16.dp))
                                    InvitedFriendsVoteStatusSection(invitedFriends = uiState.invitedFriendsVoteStatus)
                                }

                                // Add "Invite More Friends" button for poll creators
                                item {
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Button(
                                        onClick = onInviteMoreFriends,
                                        enabled = !uiState.isLoadingUninvitedFriends,
                                        modifier = Modifier.fillMaxWidth(),
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = MaterialTheme.colorScheme.secondary
                                        )
                                    ) {
                                        if (uiState.isLoadingUninvitedFriends) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(20.dp),
                                                color = MaterialTheme.colorScheme.onSecondary
                                            )
                                            Spacer(modifier = Modifier.width(8.dp))
                                        }
                                        Text("Invite More Friends")
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {
                     Column(
                        modifier = Modifier.fillMaxSize().padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(text = "An unexpected error occurred or poll data is unavailable.")
                    }
                }
            }
        }
    }

    // Add Friend Dialog
    if (uiState.showAddFriendDialog) {
        AddFriendToPollDialog(
            uninvitedFriends = uiState.uninvitedFriends,
            isAddingFriend = uiState.isAddingFriend,
            onDismiss = onDismissAddFriendDialog,
            onAddFriend = onAddFriendToPoll
        )
    }
}

/**
 * Composable to display a single poll result item (option text, progress bar, percentage).
 */
@Composable
fun PollResultItem(result: OptionResultUiState) {
    Column(modifier = Modifier.padding(vertical = 4.dp)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = result.text,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(1f)
            )
            Text(
                text = "${(result.percentage * 100).toInt()}%",
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        Spacer(modifier = Modifier.height(4.dp))
        // Single-piece progress bar
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(10.dp)
                .background(
                    color = MaterialTheme.colorScheme.surfaceVariant,
                    shape = RoundedCornerShape(5.dp)
                )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(result.percentage)
                    .height(10.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primary,
                        shape = RoundedCornerShape(5.dp)
                    )
            )
        }
    }
}

@Preview(showBackground = true, name = "Poll Details - Results Shown")
@Composable
fun PollDetailsScreenResultsPreview() {
    val samplePollWithVotes = PrivateTextPoll(
        pollId = "pollWithVotes123",
        question = "Which of these is your favorite fruit?",
        options = listOf(
            PollOption("optA", "Apple", 2),
            PollOption("optB", "Banana", 5),
            PollOption("optC", "Cherry", 1)
        ),
        creatorName = "FruitLover",
        createdAt = Timestamp(Date()),
        voteCount = 8
    )
    val sampleUserVote = PollVote(pollId = "pollWithVotes123", voterId = "userXYZ", optionId = "optB")
    val sampleOptionResults = listOf(
        OptionResultUiState("optA", "Apple", 2, 2f / 8f),
        OptionResultUiState("optB", "Banana", 5, 5f / 8f),
        OptionResultUiState("optC", "Cherry", 1, 1f / 8f)
    )

    MaterialTheme {
        PollDetailsScreenContent(
            uiState = PollDetailsUiState(
                isLoading = false,
                poll = samplePollWithVotes,
                userVote = sampleUserVote,
                optionResults = sampleOptionResults
            ),
            snackbarHostState = SnackbarHostState(),
            onNavigateBack = {},
            onOptionSelected = {},
            onSubmitVote = {}
        )
    }
}

// New Composable for displaying the list of invited friends and their vote status
@Composable
fun InvitedFriendsVoteStatusSection(invitedFriends: List<InvitedFriendVoteStatusViewData>) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = stringResource(R.string.invited_friends_vote_status_title),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                invitedFriends.forEachIndexed { index, friendStatus ->
                    InvitedFriendVoteStatusItem(friendStatus = friendStatus)
                    if (index < invitedFriends.size - 1) { // Don't add divider after last item
                        HorizontalDivider(modifier = Modifier.padding(vertical = 4.dp))
                    }
                }
            }
        }
    }
}

// New Composable for a single item in the invited friends vote status list
@Composable
fun InvitedFriendVoteStatusItem(friendStatus: InvitedFriendVoteStatusViewData) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.weight(1f)) {
            AsyncImage(
                model = friendStatus.avatarUrl,
                contentDescription = stringResource(R.string.user_avatar_content_description, friendStatus.username),
                placeholder = painterResource(id = R.drawable.default_profile_pic),
                error = painterResource(id = R.drawable.default_profile_pic),
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
            )
            Text(
                text = friendStatus.username,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(start = 8.dp)
            )
        }

        Text(
            text = if (friendStatus.hasVoted) {
                friendStatus.voteOptionText ?: stringResource(R.string.voted_status_unknown_option)
            } else {
                stringResource(R.string.not_voted_status)
            },
            style = MaterialTheme.typography.bodyMedium,
            color = if (friendStatus.hasVoted) MaterialTheme.colorScheme.onSurface else Color.Red,
            fontWeight = if (!friendStatus.hasVoted) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * Dialog for selecting friends to add to the poll.
 */
@Composable
fun AddFriendToPollDialog(
    uninvitedFriends: List<app.donskey.cantdecide.data.models.User>,
    isAddingFriend: Boolean,
    onDismiss: () -> Unit,
    onAddFriend: (String) -> Unit
) {
    AlertDialog(
        onDismissRequest = { if (!isAddingFriend) onDismiss() },
        title = {
            Text(
                text = "Invite Friends to Poll",
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            if (uninvitedFriends.isEmpty()) {
                Text(
                    text = "No uninvited friends available.",
                    style = MaterialTheme.typography.bodyMedium
                )
            } else {
                LazyColumn(
                    modifier = Modifier.heightIn(max = 300.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(uninvitedFriends) { friend ->
                        FriendSelectionItem(
                            friend = friend,
                            isAddingFriend = isAddingFriend,
                            onAddFriend = { onAddFriend(friend.uid) }
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isAddingFriend
            ) {
                Text("Cancel")
            }
        },
        dismissButton = null
    )
}

/**
 * Individual friend item in the selection dialog.
 */
@Composable
fun FriendSelectionItem(
    friend: app.donskey.cantdecide.data.models.User,
    isAddingFriend: Boolean,
    onAddFriend: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.weight(1f)
            ) {
                AsyncImage(
                    model = friend.photoUrl,
                    contentDescription = "Friend avatar",
                    placeholder = painterResource(id = R.drawable.default_profile_pic),
                    error = painterResource(id = R.drawable.default_profile_pic),
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = friend.displayName,
                    style = MaterialTheme.typography.bodyLarge
                )
            }

            Button(
                onClick = onAddFriend,
                enabled = !isAddingFriend,
                modifier = Modifier.height(36.dp)
            ) {
                if (isAddingFriend) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Text("Invite")
                }
            }
        }
    }
}
