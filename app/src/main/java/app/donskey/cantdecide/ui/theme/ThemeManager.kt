package app.donskey.cantdecide.ui.theme

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.graphics.Color

/**
 * Singleton class to manage theme preferences across the app.
 * Maintains state for dark mode and accent color, provides methods
 * for theme customization and persistence.
 */
object ThemeManager {
    // Define new color constants
    private val COLOR_PURPLE = Color(0xFF9C88FF) // Lighter purple
    private val COLOR_BLUE = Color(0xFF2196F3)
    private val COLOR_AQUA = Color(0xFF00BCD4) // Cyan/Aqua
    private val COLOR_GREEN = Color(0xFF4CAF50)
    private val COLOR_LIME = Color(0xFFCDDC39)
    private val COLOR_YELLOW = Color(0xFFFFC107)
    private val COLOR_ORANGE = Color(0xFFFF9800)
    private val COLOR_RED = Color(0xFFE91E63)
    private val COLOR_PINK = Color(0xFFF48FB1) // Light Pink
    private val COLOR_BLACK = Color(0xFF000000)
    private val COLOR_GREY = Color(0xFF9E9E9E)
    private val COLOR_WHITE = Color(0xFFFFFFFF)

    // Define accent color options in the desired order for the grid
    val accentColorOptions = listOf(
        AccentColorOption("Purple", COLOR_PURPLE),   // Row 1
        AccentColorOption("Blue", COLOR_BLUE),
        AccentColorOption("Aqua", COLOR_AQUA),
        AccentColorOption("Green", COLOR_GREEN),     // Row 2
        AccentColorOption("Lime", COLOR_LIME),
        AccentColorOption("Yellow", COLOR_YELLOW),
        AccentColorOption("Orange", COLOR_ORANGE),   // Row 3
        AccentColorOption("Red", COLOR_RED),
        AccentColorOption("Pink", COLOR_PINK),
        AccentColorOption("Black", COLOR_BLACK),     // Row 4
        AccentColorOption("Grey", COLOR_GREY),
        AccentColorOption("White", COLOR_WHITE)
    )

    // Dark mode state
    private val _isDarkMode = mutableStateOf(false)
    val isDarkMode: State<Boolean> = _isDarkMode

    // Selected accent color index state - use mutableIntStateOf for integers
    private val _selectedColorIndex = mutableIntStateOf(1) // Default is Blue (index 1)

    // Current accent color based on selected index
    val currentAccentColor: Color
        get() = accentColorOptions[_selectedColorIndex.intValue].color

    /**
     * Updates the dark mode setting.
     * @param isDark Boolean Whether dark mode is enabled
     */
    fun setDarkMode(isDark: Boolean) {
        _isDarkMode.value = isDark
    }

    /**
     * Updates the selected accent color.
     * @param index Int The index of the selected accent color
     */
    fun setAccentColor(index: Int) {
        if (index in accentColorOptions.indices) {
            _selectedColorIndex.intValue = index
        }
    }

    /**
     * Initializes theme preferences from saved user settings.
     * @param isDark Boolean Whether dark mode is enabled
     * @param colorIndex Int The index of the selected accent color
     */
    fun initializeFromPreferences(isDark: Boolean, colorIndex: Int) {
        setDarkMode(isDark)
        setAccentColor(colorIndex)
    }

    /**
     * Resets theme settings to default values.
     * This is used when user logs out to ensure the login and verification screens
     * use default theme settings instead of the previous user's preferences.
     */
    fun resetToDefaults() {
        _isDarkMode.value = false  // Default to light mode
        _selectedColorIndex.intValue = 1  // Default to Blue (index 1), use .intValue
    }
}