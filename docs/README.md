### Prerequisites

- ✅ Install Android Studio Meerkat
- ✅ JDK 11 or newer
- ✅ Android SDK with API level 35 (Android 15)
- ✅ Android Emulator or physical device running Android 5.0 (API 21) or higher
- ✅ Create cant-decide project with Jetpack Compose bottom navigation implementation (Navigation setup
  can be customized with icons and labels as needed)
- ✅ Enable Firebase Phone login, create all the user collections (see firebaseInfo.md), populate the
  SHA-1 signatures in Firebase
- ✅ Create firestore.rules file and integrate it with Firebase

### Tasks for claude

- claude to check and verify if happy with rules, delete or add more rules if needed
- claude to update gradle files and toml files with the required plugins, dependencies etc
- claude to check collections structure in firebaseInfo.md, please discuss if you need more collections
  or fields or you want me to delete any fields. (Place tick next to this task when we're happy)
- Project uses Jetpack Compose, keep pages consistent with Jetpack compose

## Project Structure

The project is to follow clean architecture principles with the following layers:

- **Presentation**: UI components using Jetpack Compose
- **Domain**: Business logic and models
- **Data**: Data sources and repositories in Firebase

# App name "Can't Decide" (General Overview, see Detailed Overview below for a broader description)

A social decision-making app that helps users make decisions by creating polls and sharing them
with friends, family, or the public.

## Features

- User authentication via phone number only, (similar to WhatsUp) integrating Firebase
  backend (see also firebaseInfo.md)
  - Token confirmation number sent for authentication
  - No email or password required (will be optional in the user profile.
- User will be able to find friends via their contacts phone numbers or type their phone number
- Request will be sent to friends to join group if they have app installed, if they don't have app,
  a link to download the app will be sent.
- User will be able to create polls with 2-6 options (images or text)
- Images should be able to be uploaded from a link or from their saved files(eg photos) in phone
- Share polls with private groups or the public
- User to be able to select who can vote in their group. Friends to have a checkbox for selection
- Vote on polls and view real-time results
- Receive notifications for poll activity
- Add the appropriate code to save collections Data/Document to Firestore
- Add appropriate toast messages throughout the app.

## Theming Note (For AI Agent)

- Ensure user-selected accent colors (typically `MaterialTheme.colorScheme.primary` and `MaterialTheme.colorScheme.onPrimary`) are consistently applied to relevant UI components.
- This includes `TopAppBar` (container, title, icons), `Button` (container, content), `Card` elements acting as interactive items (icon tints, text colors), and other prominent UI elements that should reflect the user's chosen theme.
- A `Card` should generally remain `MaterialTheme.colorScheme.surface` or `surfaceVariant`, with its internal content (icons, specific text) potentially using the accent color for emphasis if appropriate.
