❌ ✅ Icons to use

# Project Progress Tracker

- This document tracks the progress of features and tasks for the Can't Decide app. There are
  six numbered sections and each section has sub-sections designated by alphabet.
- Each section to be completed in order. Do not progress to next section until
  the current section is completed, tested and functional. Eg If up to section 2, section 2a to
  completed and tested before moving onto section 2b, then after 2b tested and functional move to
  section 2c etc etc.
- Update this document as you progress through file and apply a tick next to any completed tasks
- Each section has a more detailed description of the features and tasks to be completed.
- Keep the default theme that android studio created for light mode, dark mode option to
  create later when we do settings page.

  ✅ 1. - Integrate google service and firebase. Gradle Version Catalog (via libs.versions.toml)
  which uses alias for dependencies.
  ✅ 2. - Create Login Page (other pages will be created later, use placeholder pages as needed)
  ✅ 3. - Create Settings Page incorporating user profile (use placeholders as needed) Test app 4. - Create Friends Page (use placeholders as needed) Test app 5. - Create Polls Page: Test app

### 1. Firebase/Google Integration and splash page (Use Jetpack Compose for UIs) ✅

    ✅ 1.1 - Create a simple test code (button to test in firebase, toast messages)
    ✅ 1.2 - Sync/Build clear of all error and warnings
    ✅ 1.3 - Check activity in firebase
    ✅ 1.4 - Check logcat for firebase success
    ✅ 1.5 - Remove all firebase testing code including button, not needed anymore
    ✅ 1.6 - Create a splash page for when app is loading (logo_heading.png, resize to what
             you think is appropriate) image in drawable folder and has clear background
    ✅ 1.7 - AI to Sync/Build: Clear all errors and warnings
    ✅ 1.8 - Me to test and confirm all OK before going to step 2

### 2. Login Page (Use Jetpack Compose for UIs) ✅

    ✅ 2.1  - Enable phone authentication in Firebase
    ✅ 2.2  - Create test phone number with test verification code in Firebase
    ✅ 2.3  - Create phone number login page
    ✅ 2.4  - Use logo_heading.png for heading(has clear background with logo and heading combined)
              resize image to what you think would be appropriate. Image in the drawable folder
    ✅ 2.5  - Dropdown menu with countries area code eg Australia +61
    ✅ 2.6  - Phone number input field with country code prefix and validation implemented
    ✅ 2.7  - Register button (with loading indicator)
    ✅ 2.8  - Toast messages for success or failure
    ✅ 2.9  - User data to be stored in Firestore (see firebaseInfo.md for collections structure)
    ✅ 2.10 - When a first time a user logs in, the user will be directed and prompted to create
              a profile, (initially a placeholder page until login page is tested and functioning) all
              other navigation links will be inactive until the compulsory profile fields are filled out
    ✅ 2.11 - After a user has created a profile, all future logins, the user will be directed to
              the home page
    ✅ 2.12 - Use layout with 6 single-digit EditText fields in a LinearLayout for verification code
              Automatically move focus to the next field when a digit is entered, handle backspace
              navigation - move to previous field when backspace is pressed in empty field
    ✅ 2.13 - AI Sync/Build: Clear all errors and warnings
    ✅ 2.14 - Me to test and confirm all OK before going to step 3

### 3. Settings Page (Incorporating Profile) (Use Jetpack Compose for UIs) ✅

    ✅ 3.1  - User data to be stored in Firestore (see firebaseInfo.md for collections structure)
              whenever save is pressed, a tick icon would look best for save option. As we got
              through the settings features, if you think we need another collection or extra
              field(s) please discuss.
    ✅ 3.2  - Instead of modal dialogs, use similar to WhatsApp full-screen navigation to edit
              profile fields
    ✅ 3.3  - Each editable item (like profile picture, name, about etc) appears as a list item
    ✅ 3.4  - Tapping an item navigates to a dedicated edit screen with an app bar containing
              save/cancel actions
    ✅ 3.5  - as we add features, make sure page is scrollable especially when keyboard is displayed, which
              ever text field is clicked the text field must be above the keyboard, but this should not
              occur when using dedicated edit screen
    ✅ 3.6  - see attached screenshot, keep theme the same
    ✅ 3.7  - Replace Dashboard icon with Settings icon on bottom navigation bar and use Dasboard
              page for settings page.
    ✅ 3.8  - Replace Dashboard text with Settings text in the above blue status area, do
              not create a new status bar as one is already there, just change the text to Settings
    ✅ 3.9  - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.10 - Create a circular profile picture component that allows users to upload or display a
              custom image, the image should have a pen edit icon that goes to designated page
              to upload user image. Loading should happen on this page not the settings page.
    ✅ 3.11 - Image url to be saved to Firestore user collection and User Storage bucket
    ✅ 3.12 - Add a bold, centered text field below the user picture to display the user's chosen
              username or handle, with a character limit of 20." (Edit of user name to be created
              in later step.
    ✅ 3.13 - Bio input field under Display name default can be "Bio" user can change and add in
              edit mode to be added in later stop. Max of 100 characters
    ✅ 3.14 - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.15 - Insert a compact section below the bio text displaying 'Following X | Friends Y'
              in a clickable format, linking to lists of friends and followers. Create placeholder
              pages displaying "This feature coming soon" or something similar
    ✅ 3.16 - Build a horizontal stats bar under Following/Friends links showing three metrics:
              'Polls Created,' 'Votes Received,'and 'Decisions Made,' using numeric values fetched
              from the user's activity data." These are placeholders for now as the polls section is
              not created yet
    ✅ 3.17 - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.18 - Create a 3 editable fields on a card, underneath the horizontal stats bar
              "Display Name  => "  & "Bio  => " & "Email  =>"
    ✅ 3.19 - When "Display Name" is tapped it will go to dedicated edit page for editing
              Display name to be stored in Firestore (see firebaseInfo.md for collections structure)
    ✅ 3.20 - When "Bio" is tapped it will go to dedicated edit page for editing
              Bio to be stored in Firestore (see firebaseInfo.md for collections structure)
    ✅ 3.21 - When "Email" is tapped it will go to dedicated edit page for editing
    ✅ 3.22 - If user does enter an email & saves, it will need to be stored in Firebase user
              collection, and and email verfication to be sent with a toast message for user,
              when user returns to Settings page there will be text next to "Email" field saying
              "Verification Sent". If they verify, text should be changed next to Email field
              "Verified" if they don't verify then text next to Email field should say "Verify"
              that will send fresh email verification if current email verification has timed out.
            - If email is verified or pending verification no other user will be able to use
              the email, use appropriate messages notifying the user.
    ✅ 3.23 - Settings to be saved to Firestore user collection.
    ✅ 3.24 - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.25 - Create new card under previous card (same theme)  with following four editable
              fields (add appropriate icons):"Notifications Preferences", "Privacy Settings",
              "Appearance", "Account", Each field to have dedicated editable page, (See screenshot
              for current card theme)
    ✅ 3.26 - On the notifcations page add a list of the following text & features (each field to
              have an on/off toggle, make it green for on and red for off, all defaults "On"
            - Poll Invitations (Bold)
              Receive notifications when you're invited to a poll           (toggle on)
            - Vote Notifications (Bold)
              Receive notifications when someone votes on your poll         (toggle on)
            - Poll Results (Bold)
              Receive notifications when a poll you participated in ends    (toggle on)
            - Comment Notifications (Bold)
              Receive notifications for new comments on your polls          (toggle on)
            - App Updates
              Receive notifications about new app features and updates      (toggle on)
            - Friend Requests
              Receive notifications when someone sends you a friend request (toggle on)
    ✅ 3.27 - These settings to be saved to Firestore collections (see firebaseInfo.md, if any
              field(s) you think are  missing please let me know and I will add)
    ✅ 3.28 - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.29 - On the privacy settings page have following fields with same toggles as above
              (see screenshot for reference) Default to be off
            - Public Profile (Bold)
              Allow others to see your profile information                  (toggle off)
            - Visibility (Bold)
              Hide your current status (others cannot see you're signed in) (toggle off)
            - Allow Tagging (Bold)
              Allow others to tag you in polls                              (toggle on)
            - Data Privacy (Bold)
              View our privacy policy and we handle your data   => (link to page with info)
            - These settings to be saved to Firestore collections (see firebaseInfo.md, if any
              field(s) missing please let me know and I will add)
    ✅ 3.30 - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.31 - On the appearance page have following fields (see screen shot for reference)
            - Dark/light mode toggle (must reflect the user's theme preference
              throughout the app, default on Light)
            - Accent Color (Bold)
              Card with following colors
              Theme Color (Bold)
              Select your preferred accent color for app:
              Black, White, Green, Red, Yellow, Blue
            - These settings to be saved to Firestore collections (see firebaseInfo.md, if any
                  field(s) missing please let me know and I will add)
            - Add a "Restore Defaults" button
    ✅ 3.32 - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.33 - On the Account Dedicated page, have "Sign Out"  Button (Blue) and
              "Delete Account" Button Red (Both to have appropriate icons, see
              screen shot for reference)
    ✅ 3.34 - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 3.35 - After testing and all ok, remove temp Signout button from home page
    ✅ 3.36 - Me to test and confirm all OK before going to step 4

### 4. Friends/Contacts Functionality (Use Jetpack Compose for UIs)

    ✅ 4.1  - Change Notification Icon for a Friends icon and replace all relative code from
              Notifications to Friends, create new file and delete the old ones. Sync/Build/Test
    ✅ 4.2  - Set Up the Firebase Backend for Friends
    ✅ 4.3  - Initialize the Friends Collection in Firestore
       4.4  - Check Firestore Security Rules: Ensure only authenticated users can
              read/write their own friend records:
    ✅ 4.5  - Create following composite indexes in Firestore, all ascending
            - (status, userId2, initiatedBy, _name_) - For incoming friend requests
            - (userId, status, _name_) - For getting friends where user is userId
            - (userId2, status, _name_) - For getting friends where user is userId2
            - (userId, status, initiatedBy, _name_) - For outgoing friend requests
            - (userId, userId2, _name_) - For user to user2 requests
            - (userId2, userId, _name_) - For user2 to user requests
            - (initiatedBy, status, _name_) -
              The "_name_" is automatically included by Firestore in all composite indexes -
              it represents the document ID and helps with pagination and ordering, therefore
              do not include "_name_" when generating the composite indexes
    ✅ 4.6  - Implement the FriendsRepository to handle all friend-related operations and queries.
    ✅ 4.7  - Implement a test function that will trigger the queries to generate the index links.
            - AI to Sync/Build: Clear all errors and warnings & then I will test
            - Check logcat when clicking on the Friends icon for confirmation all is working ok.
    ✅  4.8 - Friends Page Approach
              Create a card with different links to dedicated pages same as settings page
              (Similar approach as Whatsup, settings see screenshot for reference and examine
              settings project files for theme and color of cards, use appropriate icons), please
              make sure page is scrollable.
            - First link "Invite A Friend      =>" (Only 1 link at this stage)
            - "Search Contacts" (search the users phone contacts,  when contact is
              selected, check if there is a match in user database of their phone number, they will
              get an invite to accept invitation or reject. If accepted Friend to be displayed in
              a list Headed "Friends" under the "Search Contacts" button
            - If there is not match display a dialogue box advising user to send an sms to their
              friend to download the app. Provide the link on the diaglogue box,
            - Use snackbars for displaying to user(s)
            - Run Firebase Cli with following commands
              firebase deploy --only functions
              npm run lint -- --fix   (to fix errors, then run deploy again)
            - Create cloud functions:
              handlefrienddeletion, handlefriendrequestcreation, updatefriendcounts
            - Make above in real time (dynamic)
            - Updating count fields in user profiles when friendships change
            - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 4.09 - Implement code for "Friend Request" notifications setting if user toggles on
            - When a user sends Friend request, recipient will get phone alert the default sound
              phone uses for notifications and a snackbar message long length display (5 seconds)
            - If app is in foreground then alert and snackbar notification
            - If app is in background then alert and system notification
            - Create a notification icon for notifications banners
            - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 4.10 - Implement code for "Visibility" Privacy notifications setting if user toggles on
            - When a user toggles on, "(Offline)" in red displayed next to their name in other
              uses friends list
            - Create syncVisibilityOnFriendshipChange cloud function
            - To be updated in real time when toggles on/off
            - AI to Sync/Build: Clear all errors and warnings & then I will test
    ✅ 4.13 - Me to test and confirm all OK before going to step 5

## 5. Polls (Use Jetpack Compose for UIs)

       ✅ 5.1 - Create a new Icon on bottom nav bar between "Polls" & "Friends" called "Create Poll"
                Use a different icon to the Polls icon signifying Voting (to discuss with AI)
                We will use the current Polls page to display the polls for voting.
                - AI to Sync/Build: Clear all errors and warnings & then I will test
       ✅ 5.2 - Instead of modal dialogs, use similar to WhatsApp full-screen navigation to edit
                polls fields listed on cards (See Settings page, use same theme & structure)
              - Editable Items(Fields): (Placeholder pages to have appropriate text coming soon or similar)
                If you can think of an approriate icon for each field see Settings screen shot, it
                it looks so much better that without an icon)
                   1st Card with editable items:
                       - Create Private Polls > Create Private Polls page (Placeholder Page)
                       - View Private Polls > View Private Polls page (Placeholer page)
                       - Private Polls History > Private Polls History page (Placeholder page)
                   2nd Card with editable items:
                       - Create Public Polls > Create Public Polls page (Placeholder Page)
                       - View Public Polls > View Puble Polls page (Placeholder Page)
                       - Public Polls History > Public Polls History page (Placeholder page)
              - AI to Sync/Build: Clear all errors and warnings & then I will test
       ✅ 5.3 - Instead of modal dialogs, use similar to WhatsApp full-screen navigation to edit
              - "Create Private Poll" page > Card (Keep card theme and color as Step 5.2)
                       - "Text Poll" > "Text Poll Page" (Maybe have some text saying "Use this link to create a poll without images")
                       - "Image Poll > "Image Poll" (Maybe have some text saying "Use this link to create a poll with images")
                       - "Video Poll > "Video Poll". (Maybe have some text saying "Use this link to create a poll with videos")
                       - Use appropriate icons for each link & Use placeholder pages with the usual coming soon stuff
              - "Create Public Poll" page > Card (Keep card theme and color as Step 5.2)
                       - "Text Poll" > "Text Poll Page" (Maybe have some text saying "Use this link to create a poll without images")
                       - "Image Poll > "Image Poll" (Maybe have some text saying "Use this link to create a poll with images")
                       - "Video Poll > "Video Poll". (Maybe have some text saying "Use this link to create a poll with videos")
                       - Use appropriate icons for each link & Use placeholder pages with the usual coming soon stuff
              - AI to Sync/Build: Clear all errors and warnings & then I will test
      ✅ 5.4  - Create Private Polls Nav:   Polls > Create Private Polls > Test Poll
                                                                         > Image Poll
                                                                         > Video Poll
      ✅ 5.5  - Create Public Polls Nav:    Polls > Create Public Polls  > Test Poll
                                                                         > Image Poll
                                                                         > Video Poll
              - AI to Sync/Build: Clear all errors and warnings & then I will test
      ✅ 5.6  - Implement Private Text Polls Saving to Firestore: (Completely get Private Text Polls working will be good template for the rest of polls)
      ✅ 5.7  - Define PollsRepository Interface: Formalize the methods needed in the repository, e.g., suspend fun createTextPoll(poll: TextPoll): Result<Unit> (or similar, to handle success/failure).
      ✅ 5.8  - Implement PollsRepositoryImpl:
      ✅ 5.9  - Write the Firestore logic to add the TextPoll object to a "polls" collection (e.g., text_polls or a general polls collection with a type field).
      ✅ 5.10 - Ensure pollId is automatically generated by Firestore or handled by the repository.
      ✅ 5.11 - Integrate Hilt (Dependency Injection):
      ✅ 5.12 - Set up Hilt in the project if not already done.
      ✅ 5.13 - Provide the PollsRepositoryImpl instance to CreatePrivateTextPollViewModel via Hilt constructor injection.
      ✅ 5.14 - Replace Placeholder User Data:
      ✅ 5.15 - Determine how to access the currently logged-in user's ID and display name within
                CreatePrivateTextPollViewModel (this might involve an AuthService or an existing
                way user data is managed).
      ✅ 5.16 - Replace TODO_current_user_id and TODO_current_user_name with actual user data.
              - Friends list will display username and avatar
              - User to select which of their friends to select to vote in their private poll
                by checkbox next to each user name, also have select all option
              - AI to Sync/Build: Clear all errors and warnings & then I will test
      ✅ 5.17 - Create Polls Nav: (1st Card Private Polls)  Polls > Create Private Polls  > Placeholder page
                                                                  > View Private Polls    > Placeholder page
                                                                  > Private Polls History > Placeholder page
                                  (2nd Card Public Polls)   Polls > Create Public Polls   > Placeholder page
                                                                  > View Public Polls     > Placeholder page
                                                                  > Public Poll History   > Placeholder page
              - AI to Sync/Build: Clear all errors and warnings & then I will test
      ✅ 5.18 - ViewModel and UI for Viewing Private Polls:
              ✅ - `ViewPrivatePollsViewModel` fetches polls created by and invited to the current user.
              ✅ - Polls are sorted by creation date (latest first).
              ✅ - Loading and error states are handled.
              ✅ - `ViewPrivatePollsScreen` displays the list of polls from the ViewModel.
              ✅ - Each poll item shows: Creator, Question, Type, Options summary, Expiry, Creation date, Vote count.
              ✅ - Creator avatar is displayed on `ViewPrivatePollsScreen`.
              ✅ - Navigation to `ViewPrivatePollsScreen` is set up.
              ✅ - Navigation from poll creation screen back to a relevant screen is implemented.
              ✅ - Delete icon is present on polls created by the current user (full logic pending).
              ✅ - Anonymous private polls are correctly sent to all of creator's friends and display correctly for both creator and friends.

      ✅ 5.19 - Enhanced Data Models and Repository for Voting:
              ✅ - `PrivateTextPoll`, `PollOption`, `PollVote` data models updated for voting (e.g., `PrivateTextPoll.anonymous`, `PollOption.voteCount`).
              ✅ - `PollsRepository` updated with methods for `getPollDetails`, `getUserVote`, `submitVote`.

      ✅ 5.20 - Real-time Poll Updates:
              ✅ - `PrivateTextPoll` includes `voteCount` and `votedBy` fields.
              ✅ - Firebase Cloud Function (`updatePollVoteCounts`) automatically updates `voteCount` and `votedBy` on new votes.
              ✅ - Repository methods (`getPrivateTextPollsCreatedByCurrentUser`, `getPrivateTextPollsInvitedToCurrentUser`, `getPollDetails`) use snapshot listeners for real-time data.

      ✅ 5.21 - Poll Details Screen for Voting:
              ✅ - `PollDetailsViewModel` observes real-time poll details and current user's vote.
              ✅ - `PollDetailsScreen` displays poll question and options (e.g., using RadioButtons).
              ✅ - Users can select an option and submit their vote from the `PollDetailsScreen`.
              ✅ - Screen correctly reflects if the user has already voted (displaying their choice and disabling further voting).
              ✅ - Navigation from `ViewPrivatePollsScreen` to `PollDetailsScreen` is implemented.
              ✅ - Navigation back from `PollDetailsScreen` to `ViewPrivatePollsScreen` refreshes the list view (e.g., vote status).
              ✅ - Vote status (e.g., "Votes: X (Voted)") updates in real-time on `ViewPrivatePollsScreen`.

       ✅ 5.22 - Implement Poll Results Display:
              ✅ - On `PollDetailsScreen`, after voting or if a poll is closed/expired (latter part pending Task 5.24), display detailed results.
              ✅ - Ensured `PollOption.voteCount` in the `PrivateTextPoll` document's `options` list is updated via the modified `updatePollVoteCounts` Cloud Function to reflect individual option counts.
              ✅ - `PollDetailsViewModel` updated to calculate percentages for each option and expose this as `optionResults` in `PollDetailsUiState`.
              ✅ - `PollDetailsScreen` now renders a `PollResultItem` for each option when results are shown.
              ✅ - `PollResultItem` displays:
                  ✅ - Option text.
                  ✅ - Percentage of votes for the option (e.g., "37%"), styled with accent color.
                  ✅ - `LinearProgressIndicator` visually representing the percentage, using user's accent color.
              ✅ - Total vote count for the poll is displayed (e.g., "Total votes: 8").
              ✅ - UI refined for a cleaner look: removed dividers between result items and removed redundant individual vote counts under each option's progress bar.
              ✅ - Spacing between result items adjusted for a more compact card.
              ✅ - After a user votes, they remain on the `PollDetailsScreen` to view the updated results.
              ✅ - A snackbar message ("Vote submitted successfully! Results updated.") is shown after successful voting.

       5.23 - Implement Full Poll Deletion Logic:
              ✅ - Currently, a delete icon shows a snackbar. Implement the actual deletion of the poll document from `private_polls` and its `votes` subcollection from Firestore.
              ✅ - Ensure only the poll creator can delete the poll.
              ✅ - When user taps the delete icon, they should be prompted with a confirem dialog box
              ✅ - Check firestore.rules and add any permissions if needed then deploy to Firestore Rules
              ✅ - Consider if any associated data needs cleanup (e.g., if vote counts are aggregated elsewhere, or if images/videos are involved in future poll types).
              ✅ - AI to Sync/Build: Clear all errors and warnings & then I will test

       ✅ 5.24 - Implement Notifcations when a poll is created
                ✅ - Send system alert notification to users selected friends in a non-anonymous poll and to all
                  friends in an anonymous poll
                ✅ - Check functions/src/index.ts, I think a function for this feature has already been created, but please check if
                  any changes to this function is needed
                ✅ - Incoporate the toggle switch for this feature found at "Settings > Notication Preferences > Poll Invitations" (is on by default)
                Test non-anonymous polls: Create with selected friends, verify only they get notifications
Test anonymous polls: Create and verify all friends get notifications
Test friend addition: Add friends manually to polls, verify validation
Test auto-invite: Become friends and check for auto-addition to anonymous polls
                ✅ - AI to Sync/Build: Clear all errors and warnings & then I will test

       5.25 - Implement Notifcations to creator of poll when a friend submits a vote...... details to follow

       5.26 - Handle Poll Statuses (Closed/Expired):
              - Implement logic to change poll status to "CLOSED" (e.g., manually by creator if desired in future) or "EXPIRED" (e.g., based on `closesAt` timestamp, possibly via a scheduled Cloud Function or client-side check when polls are loaded/viewed).
              - `PollDetailsScreen` should disallow voting on polls that are not "ACTIVE".
              - `ViewPrivatePollsScreen` and `PollDetailsScreen` could visually indicate closed/expired polls.

       5.27 - Robust Error Handling and UI/UX Polish for Polls:
              - Thoroughly test all poll-related user flows for edge cases (e.g., no friends, Firestore offline, permission errors) and network errors. Implement comprehensive error messages and recovery paths.
              - Refine UI elements, screen transitions, and overall user experience for poll creation, viewing, and voting.
              - Ensure consistency in design and behavior across all poll-related screens.
              - Address any remaining UI inconsistencies or areas for improvement.

####### Completed Tasks

### ✅ 1. Firebase/Google Integration and splash page

### ✅ 2. Login Page

### ✅ 3. Settings Page (Incorporating Profile)

### ✅ 4. Friends/Contacts Functionality

####### Pending Tasks

## 5. Polls

####### Other tasks completed in previous completed steps that were discovered during testing
✅ - Added pendingEmailVerification collection to know when an email is pending verificaton so no
other user can use the email
✅ - Created cloud functions to assist in the above task and deployed to cloud

## Possible Considerations

To do for profile
There are a few standard profile page features that could be added in the future, but they would
best be implemented after other app functionality is in place:

1. Social connections - The friends/followers section needs the underlying social functionality to
   be implemented first
2. Poll history - The active polls and past decisions sections need the core poll creation and
   voting features to work
3. User statistics - The counts (polls created, votes received, decisions made) need to be
   connected to actual app usage data
4. Edit profile picture options - Could add options to take a photo with camera or choose from
   gallery, plus cropping tools
5. Settings integration - Link to account settings, notification preferences, privacy controls, etc.
6. Account verification - Badge or indicator for verified accounts, if that's a feature you'd want
7. Activity feed - Recent actions taken by the user
8. com.hbb20:ccp:2.5.3 (CountryCodePicker) Claude to check if he likes it and add if he does.
9. Create a Polls Settings card, not sure what to add here, will know more and dicuss
   what fields to create here once we start creating some polls
10. Make email uneditable once verified, have an edit email icon to change email address
11. When user sends friend request, they should have it in a pending list until accepted or rejected
12. Paginate large friend lists (load 20-30 at a time)
13. Cache friend list locally using Room database for offline access
14. Use Firebase Cloud Functions for: Cleaning up rejected requests after a period
    Sending notifications for friend requests

####################################################################################################

## Detailed Implementations (More detail of features implemented or edge cases when testing)

- Fixed when userA is viewing Poll Options/Results and userB (Poll creator) deletes the poll userA to
  be navigated to previous page View Poll, this was needed otherwise the app would crash

// Represents a private text-based poll
data class TextPoll(

    val pollId: String = "", // Firestore document ID
    val creatorId: String = "", // UID of the user who created the poll
    val creatorName: String = "", // Display name of the creator
    val question: String = "",
    val options: List<PollOption> = emptyList(),
    val allowedVoters: List<String> = emptyList(), // List of User UIDs allowed to vote
    val anonymous: Boolean = false,
    val type: String = "TEXT_PRIVATE", // To distinguish from other poll types later
    val createdAt: Timestamp = Timestamp.now(),
    val closesAt: Timestamp? = null, // Nullable for optional expiry
    val status: String = "ACTIVE" // e.g., ACTIVE, CLOSED, EXPIRED

)
// Represents a single option in a poll

data class PollOption(

    val optionId: String = "", // Unique ID for the option (can be auto-generated)
    val text: String = ""
    // Consider adding voteCount: Int = 0 if useful for quick display later

)

// Represents an individual vote (likely in a subcollection polls/{pollId}/votes)

data class PollVote(

    val voteId: String = "", // Firestore document ID for the vote
    val pollId: String = "",
    val voterId: String = "",
    val optionId: String = "", // ID of the option chosen
    val votedAt: Timestamp = Timestamp.now()

    // If not anonymous, voterName could also be stored here

)

---
