2025-05-28 17:33:34.736 16226-16226 ViewRootIm...nActivity] app.donskey.cantdecide               I  ViewPostIme pointer 0
2025-05-28 17:33:34.761 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.772 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.782 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.793 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.802 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.813 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.824 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.835 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.846 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.855 16226-16226 ViewRootIm...nActivity] app.donskey.cantdecide               I  ViewPostIme pointer 1
2025-05-28 17:33:34.857 16226-16226 FocusDebug              app.donskey.cantdecide               D  Create Poll button clicked.
2025-05-28 17:33:34.858 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.869 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.880 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.891 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.903 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.911 16226-16226 CreatePrivateTextPollVM app.donskey.cantdecide               D  Validation passed, attempting to fetch user and create poll.
2025-05-28 17:33:34.911 16226-16226 UserRepository          app.donskey.cantdecide               D  getUserProfile: Attempting to fetch profile for UID: MDIFs5fCHggRUd34MBUHNZGSehR2
2025-05-28 17:33:34.913 16226-16226 UserRepository          app.donskey.cantdecide               D  getUserProfile: Document found for UID: MDIFs5fCHggRUd34MBUHNZGSehR2
2025-05-28 17:33:34.914 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.916 16226-16226 UserRepository          app.donskey.cantdecide               D  getUserProfile: Successfully converted document to User object. DisplayName: Donskey
2025-05-28 17:33:34.916 16226-16226 CreatePrivateTextPollVM app.donskey.cantdecide               D  Attempting to create poll: PrivateTextPoll(pollId=, creatorId=MDIFs5fCHggRUd34MBUHNZGSehR2, creatorName=Donskey , question=Test, options=[PollOption(optionId=cf544432-0893-4016-a439-02ea679b4a44, text=1, voteCount=0), PollOption(optionId=13c6744c-d2a6-49c7-a2c1-2c90447a6366, text=2, voteCount=0)], allowedVoters=[qQCRtcY3kMd7ISMXDSiZVNf0BiC3], anonymous=false, type=text, privacyLevel=private, createdAt=Timestamp(seconds=1748417614, nanoseconds=916000000), closesAt=null, status=ACTIVE, voteCount=0, votedBy=[])
2025-05-28 17:33:34.925 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.928 16226-16226 CreatePollScreen        app.donskey.cantdecide               D  Recomposition: state.numberOfOptions = 2
2025-05-28 17:33:34.937 16226-16226 CreatePollScreen        app.donskey.cantdecide               D  Dropdown: state.numberOfOptions = 2
2025-05-28 17:33:34.937 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.948 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.957 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.960 16226-16226 FocusDebug              app.donskey.cantdecide               D  Option 0 onFocusEvent: isFocused = false
2025-05-28 17:33:34.960 16226-16226 FocusDebug              app.donskey.cantdecide               D  Poll Question onFocusEvent: isFocused = false
2025-05-28 17:33:34.960 16226-16226 FocusDebug              app.donskey.cantdecide               D  Option 1 onFocusEvent: isFocused = false
2025-05-28 17:33:34.968 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.980 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:34.991 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.002 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.013 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.025 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.036 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.047 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.058 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.069 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.081 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.092 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.103 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.114 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.126 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.136 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.148 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.158 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.170 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.182 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.191 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.203 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.214 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.226 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.237 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.249 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.259 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.270 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.281 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.292 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.303 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.314 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.324 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.336 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.348 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.359 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.370 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.383 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.396 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.397 16226-16226 CreatePrivateTextPollVM app.donskey.cantdecide               D  Poll created successfully! Poll ID: KCRohu8FWeCae9kA7ZN3
2025-05-28 17:33:35.398 16226-16226 FocusDebug              app.donskey.cantdecide               D  [EFFECT General] Received: ShowError(message=Poll created successfully!)
2025-05-28 17:33:35.404 16226-16226 Toast                   app.donskey.cantdecide               I  show: caller = app.donskey.cantdecide.presentation.features.polls.create_private_text_poll.CreatePrivateTextPollScreenKt$CreatePrivateTextPollScreen$1$1.invokeSuspend:94
2025-05-28 17:33:35.404 16226-16226 Toast                   app.donskey.cantdecide               I  show: contextDispId = 0 mCustomDisplayId = -1 focusedDisplayId = 0 isActivityContext = true
2025-05-28 17:33:35.411 16226-16226 FocusDebug              app.donskey.cantdecide               D  [EFFECT General] Received: NavigateBack
2025-05-28 17:33:35.437 16226-16226 CreatePollScreen        app.donskey.cantdecide               D  Recomposition: state.numberOfOptions = 2
2025-05-28 17:33:35.441 16226-16226 CreatePollScreen        app.donskey.cantdecide               D  Dropdown: state.numberOfOptions = 2
2025-05-28 17:33:35.473 16226-16226 FocusDebug              app.donskey.cantdecide               D  Option 0 onFocusEvent: isFocused = false
2025-05-28 17:33:35.473 16226-16226 FocusDebug              app.donskey.cantdecide               D  Poll Question onFocusEvent: isFocused = false
2025-05-28 17:33:35.474 16226-16226 FocusDebug              app.donskey.cantdecide               D  Option 1 onFocusEvent: isFocused = false
2025-05-28 17:33:35.515 16226-16226 FirebaseHelper          app.donskey.cantdecide               D  Logged analytics event: screen_view
2025-05-28 17:33:35.517 16226-16287 gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-28 17:33:35.527 16226-16226 CreatePollScreen        app.donskey.cantdecide               D  Recomposition: state.numberOfOptions = 2
2025-05-28 17:33:35.544 16226-16226 CreatePollScreen        app.donskey.cantdecide               D  Dropdown: state.numberOfOptions = 2
2025-05-28 17:33:35.581 16226-16232 InputTransport          app.donskey.cantdecide               D  Input channel destroyed: 'ClientS', fd=141
2025-05-28 17:33:35.581 16226-16232 InputTransport          app.donskey.cantdecide               D  Input channel destroyed: 'ClientS', fd=164
2025-05-28 17:33:35.581 16226-16232 InputTransport          app.donskey.cantdecide               D  Input channel destroyed: 'ClientS', fd=193
2025-05-28 17:33:35.594 16226-16226 FocusDebug              app.donskey.cantdecide               D  Option 0 onFocusEvent: isFocused = false
2025-05-28 17:33:35.594 16226-16226 FocusDebug              app.donskey.cantdecide               D  Poll Question onFocusEvent: isFocused = false
2025-05-28 17:33:35.594 16226-16226 FocusDebug              app.donskey.cantdecide               D  Option 1 onFocusEvent: isFocused = false