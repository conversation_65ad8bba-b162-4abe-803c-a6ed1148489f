2025-05-27 20:15:51.086  9955-10025 Firestore               app.donskey.cantdecide               W  (25.1.1) [Firestore]: Listen for Query(target=Query(private_polls/48BIttexG1dz8v1hCYKg order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
2025-05-27 20:15:51.087  9955-9955  PollsRepositoryImpl     app.donskey.cantdecide               E  Firestore listener error for poll 48BIttexG1dz8v1hCYKg: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 20:15:51.088  9955-9955  PollDetailsViewModel    app.donskey.cantdecide               E  Error loading poll: PERMISSION_DENIED: PERMISSION_DENIED: Missing or insufficient permissions.
2025-05-27 20:15:51.088  9955-9955  PollDetailsViewModel    app.donskey.cantdecide               W  Poll access denied - likely deleted. Navigating back.
2025-05-27 20:15:51.302  9955-9955  AndroidRuntime          app.donskey.cantdecide               E  FATAL EXCEPTION: main
Process: app.donskey.cantdecide, PID: 9955
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@159824c, Dispatchers.Main.immediate]
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
... 26 more
2025-05-27 20:15:51.328  9955-9955  Process                 app.donskey.cantdecide               I  Sending signal. PID: 9955 SIG: 9
---------------------------- PROCESS STARTED (10235) for package app.donskey.cantdecide ----------------------------
2025-05-27 20:15:52.659 10235-10235 ziparchive              app.donskey.cantdecide               W  Unable to open '/data/data/app.donskey.cantdecide/code_cache/.overlay/base.apk/classes13.dm': No such file or directory
2025-05-27 20:15:52.660 10235-10235 ziparchive              app.donskey.cantdecide               W  Unable to open '/data/data/app.donskey.cantdecide/code_cache/.overlay/base.apk/classes8.dm': No such file or directory
2025-05-27 20:15:52.662 10235-10235 ziparchive              app.donskey.cantdecide               W  Unable to open '/data/app/~~Gx_ICqDygFIgq2lLsSso8Q==/app.donskey.cantdecide-M-ozXwcx5H8WCeyyLc-69w==/base.dm': No such file or directory
2025-05-27 20:15:52.662 10235-10235 ziparchive              app.donskey.cantdecide               W  Unable to open '/data/app/~~Gx_ICqDygFIgq2lLsSso8Q==/app.donskey.cantdecide-M-ozXwcx5H8WCeyyLc-69w==/base.dm': No such file or directory
2025-05-27 20:15:53.376 10235-10235 nativeloader            app.donskey.cantdecide               D  Configuring clns-4 for other apk /data/app/~~Gx_ICqDygFIgq2lLsSso8Q==/app.donskey.cantdecide-M-ozXwcx5H8WCeyyLc-69w==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~Gx_ICqDygFIgq2lLsSso8Q==/app.donskey.cantdecide-M-ozXwcx5H8WCeyyLc-69w==/lib/arm64:/data/app/~~Gx_ICqDygFIgq2lLsSso8Q==/app.donskey.cantdecide-M-ozXwcx5H8WCeyyLc-69w==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/app.donskey.cantdecide
2025-05-27 20:15:53.393 10235-10235 nativeloader            app.donskey.cantdecide               D  Load libframework-connectivity-jni.so using APEX ns com_android_tethering for caller /apex/com.android.tethering/javalib/framework-connectivity.jar: ok
2025-05-27 20:15:53.402 10235-10235 GraphicsEnvironment     app.donskey.cantdecide               V  Currently set values for:
2025-05-27 20:15:53.402 10235-10235 GraphicsEnvironment     app.donskey.cantdecide               V    angle_gl_driver_selection_pkgs=[]
2025-05-27 20:15:53.402 10235-10235 GraphicsEnvironment     app.donskey.cantdecide               V    angle_gl_driver_selection_values=[]
2025-05-27 20:15:53.403 10235-10235 GraphicsEnvironment     app.donskey.cantdecide               V  ANGLE GameManagerService for app.donskey.cantdecide: false
2025-05-27 20:15:53.403 10235-10235 GraphicsEnvironment     app.donskey.cantdecide               V  app.donskey.cantdecide is not listed in per-application setting
2025-05-27 20:15:53.403 10235-10235 GraphicsEnvironment     app.donskey.cantdecide               V  Neither updatable production driver nor prerelease driver is supported.
2025-05-27 20:15:53.510 10235-10235 Compatibil...geReporter app.donskey.cantdecide               D  Compat change id reported: 183155436; UID 10321; state: ENABLED
2025-05-27 20:15:53.533 10235-10235 SessionsDependencies    app.donskey.cantdecide               D  Dependency to CRASHLYTICS added.
2025-05-27 20:15:53.542 10235-10235 FirebaseApp             app.donskey.cantdecide               I  Device unlocked: initializing all Firebase APIs for app [DEFAULT]
2025-05-27 20:15:53.544 10235-10235 Compatibil...geReporter app.donskey.cantdecide               D  Compat change id reported: 3400644; UID 10321; state: ENABLED
2025-05-27 20:15:53.615 10235-10262 skey.cantdecide         app.donskey.cantdecide               W  ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk*109608243]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar*825940897]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[])
2025-05-27 20:15:53.621 10235-10235 FirebaseSessions        app.donskey.cantdecide               D  Initializing Firebase Sessions SDK.
2025-05-27 20:15:53.621 10235-10262 DynamiteModule          app.donskey.cantdecide               I  Considering local module com.google.android.gms.measurement.dynamite:127 and remote module com.google.android.gms.measurement.dynamite:147
2025-05-27 20:15:53.622 10235-10262 DynamiteModule          app.donskey.cantdecide               I  Selected remote version of com.google.android.gms.measurement.dynamite, version >= 147
2025-05-27 20:15:53.622 10235-10262 DynamiteModule          app.donskey.cantdecide               V  Dynamite loader version >= 2, using loadModule2NoCrashUtils
2025-05-27 20:15:53.633 10235-10235 FirebaseCrashlytics     app.donskey.cantdecide               I  Initializing Firebase Crashlytics 19.2.1 for app.donskey.cantdecide
2025-05-27 20:15:53.644 10235-10262 System                  app.donskey.cantdecide               W  ClassLoader referenced unknown path:
2025-05-27 20:15:53.645 10235-10262 nativeloader            app.donskey.cantdecide               D  Configuring clns-5 for other apk . target_sdk_version=36, uses_libraries=, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-27 20:15:53.664 10235-10262 skey.cantdecide         app.donskey.cantdecide               W  ClassLoaderContext classpath size mismatch. expected=1, found=3 (DLC[];PCL[base.apk*109608243]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar*825940897]#PCL[/system/framework/org.apache.http.legacy.jar***********]} | DLC[];PCL[/data/data/app.donskey.cantdecide/code_cache/.overlay/base.apk/classes13.dex***********:/data/data/app.donskey.cantdecide/code_cache/.overlay/base.apk/classes8.dex***********:/data/app/~~Gx_ICqDygFIgq2lLsSso8Q==/app.donskey.cantdecide-M-ozXwcx5H8WCeyyLc-69w==/base.apk*45145480])
2025-05-27 20:15:53.667 10235-10235 SessionsDependencies    app.donskey.cantdecide               D  Subscriber CRASHLYTICS registered.
2025-05-27 20:15:53.746 10235-10235 FirebaseInitProvider    app.donskey.cantdecide               I  FirebaseApp initialization successful
2025-05-27 20:15:53.750 10235-10266 LifecycleServiceBinder  app.donskey.cantdecide               D  Binding service to application.
2025-05-27 20:15:53.775 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               I  Application onCreate started
2025-05-27 20:15:53.776 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               I  FirebaseApp initialized successfully.
2025-05-27 20:15:53.776 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               D  Initializing App Check with DEBUG provider.
2025-05-27 20:15:53.784 10235-10270 skey.cantdecide         app.donskey.cantdecide               E  No package ID 6d found for resource ID 0x6d0b000f.
2025-05-27 20:15:53.786 10235-10264 com.google...ckProvider app.donskey.cantdecide               D  Enter this debug secret into the allow list in the Firebase Console for your project: e0f877b2-b0c8-463b-b05a-919824dfa294
2025-05-27 20:15:53.786 10235-10270 FA                      app.donskey.cantdecide               I  App measurement initialized, version: 125000
2025-05-27 20:15:53.786 10235-10270 FA                      app.donskey.cantdecide               I  To enable debug logging run: adb shell setprop log.tag.FA VERBOSE
2025-05-27 20:15:53.787 10235-10270 FA                      app.donskey.cantdecide               I  To enable faster debug mode event logging run:
adb shell setprop debug.firebase.analytics.app app.donskey.cantdecide
2025-05-27 20:15:53.944 10235-10257 FirebaseCrashlytics     app.donskey.cantdecide               I  No version control information found
2025-05-27 20:15:53.944 10235-10235 skey.cantdecide         app.donskey.cantdecide               W  Long monitor contention with owner Firebase Background Thread #0 (10257) at void dalvik.system.DexPathList$Element.maybeInit()(DexPathList.java:771) waiters=0 in void dalvik.system.DexPathList$Element.maybeInit() for 147ms
2025-05-27 20:15:53.972 10235-10235 FirebaseAuth            app.donskey.cantdecide               D  Notifying id token listeners about user ( MDIFs5fCHggRUd34MBUHNZGSehR2 ).
2025-05-27 20:15:53.979 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               I  Firebase Auth persistence check complete
2025-05-27 20:15:53.979 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               D  Auth instance: com.google.firebase.auth.internal.zzab@e4c4cd2
2025-05-27 20:15:53.979 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               D  Current user: MDIFs5fCHggRUd34MBUHNZGSehR2
2025-05-27 20:15:53.979 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               I  Application onCreate finished
2025-05-27 20:15:53.980 10235-10267 CantDecideApp_DEBUG     app.donskey.cantdecide               D  Refreshing auth token for persistence
2025-05-27 20:15:53.981 10235-10269 CantDecideApp_DEBUG     app.donskey.cantdecide               D  Checking Firestore existence for user: MDIFs5fCHggRUd34MBUHNZGSehR2 in Application
2025-05-27 20:15:53.999 10235-10235 SessionLifecycleService app.donskey.cantdecide               D  Service bound to new client on process 10235
2025-05-27 20:15:54.002 10235-10274 System                  app.donskey.cantdecide               W  Ignoring header X-Firebase-Locale because its value was null.
2025-05-27 20:15:54.006 10235-10294 SessionLifecycleService app.donskey.cantdecide               D  App has not yet foregrounded. Using previously stored session: null
2025-05-27 20:15:54.006 10235-10294 SessionLifecycleService app.donskey.cantdecide               D  Client android.os.Messenger@185f4ff bound at 211245530. Clients: 1
2025-05-27 20:15:54.009 10235-10235 CantDecideApp_DEBUG     app.donskey.cantdecide               I  Auth state: SIGNED IN, UID: MDIFs5fCHggRUd34MBUHNZGSehR2, Email: <EMAIL>
2025-05-27 20:15:54.018 10235-10235 SessionLifecycleClient  app.donskey.cantdecide               D  Connected to SessionLifecycleService. Queue size 0
2025-05-27 20:15:54.098 10235-10271 TRuntime.C...ortBackend app.donskey.cantdecide               I  Making request to: https://crashlyticsreports-pa.googleapis.com/v1/firelog/legacy/batchlog
2025-05-27 20:15:54.113 10235-10305 DynamiteModule          app.donskey.cantdecide               W  Local module descriptor class for com.google.android.gms.providerinstaller.dynamite not found.
2025-05-27 20:15:54.121 10235-10304 ConnectivityManager     app.donskey.cantdecide               D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4709)] [android.net.ConnectivityManager.registerDefaultNetworkCallbackForUid(ConnectivityManager.java:5433)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5400)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5374)] [com.google.firebase.firestore.remote.AndroidConnectivityMonitor.configureNetworkMonitoring(AndroidConnectivityMonitor.java:87)] [com.google.firebase.firestore.remote.AndroidConnectivityMonitor.<init>(AndroidConnectivityMonitor.java:64)] [com.google.firebase.firestore.remote.RemoteComponenetProvider.createConnectivityMonitor(RemoteComponenetProvider.java:94)] [com.google.firebase.firestore.remote.RemoteComponenetProvider.initialize(RemoteComponenetProvider.java:41)] [com.google.firebase.firestore.core.ComponentProvider.initialize(ComponentProvider.java:158)] [com.google.firebase.firestore.core.FirestoreClient.initialize(FirestoreClient.java:284)] [com.google.firebase.firestore.core.FirestoreClient.lambda$new$0$com-google-firebase-firestore-core-FirestoreClient(FirestoreClient.java:109)] [com.google.firebase.firestore.core.FirestoreClient$$ExternalSyntheticLambda10.run(D8$$SyntheticClass:0)] [com.google.firebase.firestore.util.AsyncQueue.lambda$enqueue$2(AsyncQueue.java:445)] [com.google.firebase.firestore.util.AsyncQueue$$ExternalSyntheticLambda4.call(D8$$SyntheticClass:0)] [com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor.lambda$executeAndReportResult$1(AsyncQueue.java:330)] [com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$$ExternalSyntheticLambda2.run(D8$$SyntheticClass:0)] [java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)] [java.util.concurrent.FutureTask.run(FutureTask.java:264)] [java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)] [java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)] [java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)] [com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)] [java.lang.Thread.run(Thread.java:1012)]
2025-05-27 20:15:54.125 10235-10305 DynamiteModule          app.donskey.cantdecide               I  Considering local module com.google.android.gms.providerinstaller.dynamite:0 and remote module com.google.android.gms.providerinstaller.dynamite:0
2025-05-27 20:15:54.125 10235-10305 ProviderInstaller       app.donskey.cantdecide               W  Failed to load providerinstaller module: No acceptable module com.google.android.gms.providerinstaller.dynamite found. Local version is 0 and remote version is 0.
2025-05-27 20:15:54.141 10235-10305 nativeloader            app.donskey.cantdecide               D  Configuring clns-6 for other apk /system/framework/org.apache.http.legacy.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-27 20:15:54.141 10235-10305 nativeloader            app.donskey.cantdecide               D  Extending system_exposed_libraries: libface_landmark.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libaudiomirroring_jni.audiomirroring.samsung.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtensorflowLite.myfilter.camera.samsung.so:libtensorflowlite_inference_api.myfilter.camera.samsung.so:libFace_Landmark_API.camera.samsung.so:libHpr_RecGAE_cvFeature_v1.0.camera.samsung.so:libHprFace_GAE_api.camera.samsung.so:libFacialBasedSelfieCorrection.camera.samsung.so:libHprFace_GAE_jni.camera.samsung.so:libcolor_engine.camera.samsung.so:libDLInterface_aidl.camera.samsung.so:libObjectAndSceneClassification_2.5_OD.camera.samsung.so:libSceneDetector_v1.camera.samsung.so:libQREngine.camera.samsung.so:libEventDetector.camera.samsung.so:libFood.camera.samsung.so:libFoodDetector.camera.samsung.so:libPortraitSolution.camera.s
2025-05-27 20:15:54.152 10235-10305 nativeloader            app.donskey.cantdecide               D  Configuring clns-7 for other apk /system/framework/com.android.media.remotedisplay.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-27 20:15:54.152 10235-10305 nativeloader            app.donskey.cantdecide               D  Extending system_exposed_libraries: libface_landmark.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libaudiomirroring_jni.audiomirroring.samsung.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtensorflowLite.myfilter.camera.samsung.so:libtensorflowlite_inference_api.myfilter.camera.samsung.so:libFace_Landmark_API.camera.samsung.so:libHpr_RecGAE_cvFeature_v1.0.camera.samsung.so:libHprFace_GAE_api.camera.samsung.so:libFacialBasedSelfieCorrection.camera.samsung.so:libHprFace_GAE_jni.camera.samsung.so:libcolor_engine.camera.samsung.so:libDLInterface_aidl.camera.samsung.so:libObjectAndSceneClassification_2.5_OD.camera.samsung.so:libSceneDetector_v1.camera.samsung.so:libQREngine.camera.samsung.so:libEventDetector.camera.samsung.so:libFood.camera.samsung.so:libFoodDetector.camera.samsung.so:libPortraitSolution.camera.s
2025-05-27 20:15:54.163 10235-10305 skey.cantdecide         app.donskey.cantdecide               W  Loading /data/misc/apexdata/com.android.art/dalvik-cache/arm64/system@<EMAIL>@classes.odex non-executable as it requires an image which we failed to load
2025-05-27 20:15:54.168 10235-10305 nativeloader            app.donskey.cantdecide               D  Configuring clns-8 for other apk /system/framework/com.android.location.provider.jar. target_sdk_version=36, uses_libraries=ALL, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-27 20:15:54.168 10235-10305 nativeloader            app.donskey.cantdecide               D  Extending system_exposed_libraries: libface_landmark.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libaudiomirroring_jni.audiomirroring.samsung.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtensorflowLite.myfilter.camera.samsung.so:libtensorflowlite_inference_api.myfilter.camera.samsung.so:libFace_Landmark_API.camera.samsung.so:libHpr_RecGAE_cvFeature_v1.0.camera.samsung.so:libHprFace_GAE_api.camera.samsung.so:libFacialBasedSelfieCorrection.camera.samsung.so:libHprFace_GAE_jni.camera.samsung.so:libcolor_engine.camera.samsung.so:libDLInterface_aidl.camera.samsung.so:libObjectAndSceneClassification_2.5_OD.camera.samsung.so:libSceneDetector_v1.camera.samsung.so:libQREngine.camera.samsung.so:libEventDetector.camera.samsung.so:libFood.camera.samsung.so:libFoodDetector.camera.samsung.so:libPortraitSolution.camera.s
2025-05-27 20:15:54.180 10235-10305 skey.cantdecide         app.donskey.cantdecide               W  Loading /data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/oat/arm64/base.odex non-executable as it requires an image which we failed to load
2025-05-27 20:15:54.189 10235-10305 nativeloader            app.donskey.cantdecide               D  Configuring clns-9 for other apk /data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk. target_sdk_version=36, uses_libraries=, library_path=/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/lib/arm64:/data/app/~~S9ubu0yca15qASjyNNIVkg==/com.google.android.gms-_KALIfJ8k-83OpavkHtiow==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
2025-05-27 20:15:54.194 10235-10305 ProviderInstaller       app.donskey.cantdecide               W  Failed to report request stats: com.google.android.gms.common.se