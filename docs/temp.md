handlefriendrequestcreation
2025-05-30 18:45:41.402
FriendRequestCreation: TRIGGERED for friends/bmpdZDF1f4yAessVuSoU. Event ID: bc074513-eb21-49d3-816a-6576e85d51de
2025-05-30 18:45:41.403
FriendRequestCreation: DATA for bmpdZDF1f4yAessVuSoU - Status: pending, Sender: MDIFs5fCHggRUd34MBUHNZGSehR2, Recipient: SYsezxujHCOqt6CWHDk7pbi0jXw2, InitiatedBy: MDIFs5fCHggRUd34MBUHNZGSehR2.
2025-05-30 18:45:41.403
FriendRequestCreation: PASSED INITIAL CHECKS for bmpdZDF1f4yAessVuSoU. Proceeding with count and notification.
2025-05-30 18:45:41.403
FriendRequestCreation: Attempting to increment pending count for recipient SYsezxujHCOqt6CWHDk7pbi0jXw2 (for bmpdZDF1f4yAessVuSoU).
2025-05-30 18:45:43.175
FriendRequestCreation: SUCCESS - Incremented pending count for recipient SYsezxujHCOqt6CWHDk7pbi0jXw2 (for bmpdZDF1f4yAessVuSoU).
2025-05-30 18:45:43.175
FriendRequestCreation: Attempting to send notification to SYsezxujHCOqt6CWHDk7pbi0jXw2 from MDIFs5fCHggRUd34MBUHNZGSehR2 (for bmpdZDF1f4yAessVuSoU).
2025-05-30 18:45:43.429
FriendRequestCreation: Fetched recipient SYsezxujHCOqt6CWHDk7pbi0jXw2 data for bmpdZDF1f4yAessVuSoU.
2025-05-30 18:45:43.429
FriendRequestCreation: Recipient SYsezxujHCOqt6CWHDk7pbi0jXw2 has notifications ENABLED for bmpdZDF1f4yAessVuSoU.
2025-05-30 18:45:43.429
FriendRequestCreation: Recipient SYsezxujHCOqt6CWHDk7pbi0jXw2 has FCM token for bmpdZDF1f4yAessVuSoU: dBnLYEOURoS31-2Djr7r...
2025-05-30 18:45:44.438
FriendRequestCreation: Fetched sender MDIFs5fCHggRUd34MBUHNZGSehR2 display name 'Donskey ' for bmpdZDF1f4yAessVuSoU.
2025-05-30 18:45:44.439
FriendRequestCreation: SENDING FCM to SYsezxujHCOqt6CWHDk7pbi0jXw2 for bmpdZDF1f4yAessVuSoU. Payload: {"token":"dBnLYEOURoS31-2Djr7rmp:APA91bFqb6QYq64VC6SRrDSIHn6aKgBv6jNKOEY_PmDtcjeNuGTlqLSigu5eDE4m_euDDsZgf6tcdOsL59EboOgcklNPhS9XVJxovrhe8agg-puu5p4ODbE","data":{"type":"friend_request","title":"New F...
2025-05-30 18:45:44.542
FriendRequestCreation: SUCCESS - Sent FCM to SYsezxujHCOqt6CWHDk7pbi0jXw2 for bmpdZDF1f4yAessVuSoU.
2025-05-30 18:45:44.542
FriendRequestCreation: COMPLETED for bmpdZDF1f4yAessVuSoU.



__________________________________________________________________
2025-05-30 17:20:27.931
AutoInviteAnonymous: TRIGGERED for friendship T95UPhzDVMnupw6Y0OpV. Event ID: eb22367a-a25d-48d3-ba95-fdaf6340bff6
2025-05-30 17:20:27.931
AutoInviteAnonymous: EXIT (NOT_ACCEPTED) for T95UPhzDVMnupw6Y0OpV. Status: pending
2025-05-30 17:22:15.041

POST

200

82 B

6 ms

APIs-Google; (+https://developers.google.com/webmasters/APIs-Google.html)
https://autoinvitefriendstoanonymouspolls-5luo2aidnq-uc.a.run.app/?__GCP_CloudEventsMode=CE_PUBSUB_BINDING
2025-05-30 17:22:15.065
AutoInviteAnonymous: TRIGGERED for friendship zgUtsprljE0DGSl3zkt6. Event ID: 63b9fd29-a470-453c-9c51-d6afd9099798
2025-05-30 17:22:15.065
AutoInviteAnonymous: EXIT (NOT_ACCEPTED) for zgUtsprljE0DGSl3zkt6. Status: pending
2025-05-30 17:32:25.907

run.googleapis.com

…rnalServices.ReplaceInternalService

…rvices/autoinvitefriendstoanonymouspolls

service-114107635568@gcf-…
audit_log, method: "google.cloud.serverless.internal.InternalServices.ReplaceInternalService", principal_email: "<EMAIL>"
2025-05-30 17:32:26.622

cloudfunctions.googleapis.com

…s.v2.FunctionService.UpdateFunction

…ctions/autoInviteFriendsToAnonymousPolls

<EMAIL>
audit_log, method: "google.cloud.functions.v2.FunctionService.UpdateFunction", principal_email: "<EMAIL>"
2025-05-30 17:32:27.069

run.googleapis.com

…rnalServices.ReplaceInternalService

…rvices/autoinvitefriendstoanonymouspolls

service-114107635568@gcf-…
audit_log, method: "google.cloud.serverless.internal.InternalServices.ReplaceInternalService", principal_email: "<EMAIL>"
2025-05-30 17:32:31.459
Default STARTUP TCP probe succeeded after 1 attempt for container "worker" on port 8080.
2025-05-30 17:32:31.545

run.googleapis.com

…rnalServices.ReplaceInternalService

…oinvitefriendstoanonymouspolls-00004-taw
Ready condition status changed to True for Revision autoinvitefriendstoanonymouspolls-00004-taw with message: Deploying revision succeeded in 4.24s.
2025-05-30 17:32:32.884

run.googleapis.com

…rnalServices.ReplaceInternalService

…rvices/autoinvitefriendstoanonymouspolls
Ready condition status changed to True for Service autoinvitefriendstoanonymouspolls.
2025-05-30 17:33:14.113

cloudfunctions.googleapis.com

…s.v2.FunctionService.UpdateFunction

…ctions/autoInviteFriendsToAnonymousPolls

<EMAIL>
audit_log, method: "google.cloud.functions.v2.FunctionService.UpdateFunction", principal_email: "<EMAIL>"
