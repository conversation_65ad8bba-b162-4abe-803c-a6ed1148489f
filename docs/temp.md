INFO 2025-05-29T10:07:51.820544Z PollInvitation: Creator MDIFs5fCHggRUd34MBUHNZGSehR2 has 4 friends: tRnIVaGTglTk5H1k6R4QagQMxyi1, qQCRtcY3kMd7ISMXDSiZVNf0BiC3, ZjDuIBUFQ1e2L5v4ySmvoh5Lsml1, hFNVo8WMAPPlo1xs9RriwunBXDA2
INFO 2025-05-29T10:07:51.820880Z PollInvitation: DEBUG - AllowedVoters for non-anonymous poll JLhUcwlZXJxqoigGDoSE: Value: ["ZjDuIBUFQ1e2L5v4ySmvoh5Lsml1","hFNVo8WMAPPlo1xs9RriwunBXDA2"], Type: object, IsArray: true, Length: 2
INFO 2025-05-29T10:07:51.821084Z PollInvitation: Non-anonymous poll JLhUcwlZXJxqoigGDoSE. 2 voters specified, 2 validated as friends: ZjDuIBUFQ1e2L5v4ySmvoh5Lsml1, hFNVo8WMAPPlo1xs9RriwunBXDA2
INFO 2025-05-29T10:07:51.821170Z PollInvitation: Processing recipient ZjDuIBUFQ1e2L5v4ySmvoh5Lsml1 for poll JLhUcwlZXJxqoigGDoSE.
INFO 2025-05-29T10:07:52.040527Z PollInvitation: Recipient ZjDuIBUFQ1e2L5v4ySmvoh5Lsml1 has pollInvitations ENABLED and FCM token for poll JLhUcwlZXJxqoigGDoSE.
INFO 2025-05-29T10:07:52.040832Z PollInvitation: SENDING FCM to ZjDuIBUFQ1e2L5v4ySmvoh5Lsml1 for poll JLhUcwlZXJxqoigGDoSE.
INFO 2025-05-29T10:07:52.170046Z PollInvitation: SUCCESS - Sent FCM to ZjDuIBUFQ1e2L5v4ySmvoh5Lsml1 for poll JLhUcwlZXJxqoigGDoSE.
INFO 2025-05-29T10:07:52.170273Z PollInvitation: Processing recipient hFNVo8WMAPPlo1xs9RriwunBXDA2 for poll JLhUcwlZXJxqoigGDoSE.
INFO 2025-05-29T10:07:53.062035Z PollInvitation: Recipient hFNVo8WMAPPlo1xs9RriwunBXDA2 has pollInvitations ENABLED and FCM token for poll JLhUcwlZXJxqoigGDoSE.
INFO 2025-05-29T10:07:53.062149Z PollInvitation: SENDING FCM to hFNVo8WMAPPlo1xs9RriwunBXDA2 for poll JLhUcwlZXJxqoigGDoSE.
INFO 2025-05-29T10:07:53.112477Z PollInvitation: SUCCESS - Sent FCM to hFNVo8WMAPPlo1xs9RriwunBXDA2 for poll JLhUcwlZXJxqoigGDoSE.
  {
    "textPayload": "PollInvitation: SUCCESS - Sent FCM to hFNVo8WMAPPlo1xs9RriwunBXDA2 for poll JLhUcwlZXJxqoigGDoSE.",
    "insertId": "683831f90001b75dd42a3cac",
    "resource": {
      "type": "cloud_run_revision",
      "labels": {
        "service_name": "sendpollinvitationnotification",
        "revision_name": "sendpollinvitationnotification-00009-wol",
        "location": "us-central1",
        "project_id": "cant-decide-app",
        "configuration_name": "sendpollinvitationnotification"
      }
    },
    "timestamp": "2025-05-29T10:07:53.112477Z",
    "severity": "INFO",
    "labels": {
      "goog-drz-cloudfunctions-id": "sendpollinvitationnotification",
      "instanceId": "007f65c6d290598168304d37c2ed72fed4a709dcd9b23fc2ae35b1039f9b830326cded8f44bc251d5d2093b4b911c7ba7ff221ace119597e53c7c24005844007d39b8595022fc956bddb0e6c150a09",
      "goog-managed-by": "cloudfunctions",
      "run.googleapis.com/base_image_versions": "us-docker.pkg.dev/serverless-runtimes/google-22-full/runtimes/nodejs20:nodejs20_20250519_20_19_2_RC00",
      "goog-drz-cloudfunctions-location": "us-central1",
      "execution_id": "97nlvsrj05gj",
      "firebase-functions-hash": "d60e0c43b0dc831cb52a39ced3298404fcdaeae5"
    },
    "logName": "projects/cant-decide-app/logs/run.googleapis.com%2Fstdout",
    "trace": "projects/cant-decide-app/traces/7a8a4a67eaa53affbd6af38de093de8c",
    "receiveTimestamp": "2025-05-29T10:07:53.168387146Z",
    "spanId": "1585529798976411744"
  }
