/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import * as functions from "firebase-functions";
import * as functionsV1 from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { logger } from "firebase-functions"; // Import logger
// Import the specific v2 trigger type and event object
import {
  onDocumentUpdated,
  onDocumentDeleted,
  onDocumentCreated,
  onDocumentWritten,
  FirestoreEvent,
} from "firebase-functions/v2/firestore";
// Import QueryDocumentSnapshot for typing
import { QueryDocumentSnapshot } from "firebase-admin/firestore";
import { UserRecord } from "firebase-functions/v1/auth"; // Import UserRecord type

// Initialize the Firebase Admin SDK
// This allows the function to interact with Firestore with admin privileges
admin.initializeApp();

const db = admin.firestore();
const usersCollection = db.collection("users");

/**
 * Cloud Function triggered when a document in the 'friends' collection is updated.
 * It updates the 'friendsCount' and 'pendingRequestCount' on the relevant user documents
 * based on the status change of the friendship.
 */
// Use the v2 onDocumentUpdated trigger
export const updateFriendCounts = onDocumentUpdated(
  "friends/{friendshipId}",
  // Explicitly type the event object - Break down further
  async (
    event: FirestoreEvent<
      functions.Change<QueryDocumentSnapshot> | undefined,
      { friendshipId: string }
    >
  ) => {
    // Exit if event data or params are missing (shouldn't normally happen)
    if (!event.data || !event.params) {
      console.log("Exiting: Missing event data or params.");
      return;
    }

    // Get the change object
    const change = event.data;

    // Get the data before and after the change
    const beforeData = change.before.data();
    const afterData = change.after.data();

    // Exit if data is missing
    if (!beforeData || !afterData) {
      console.log("Exiting: Missing data snapshot before or after update.");
      return; // Use return for void functions
    }

    const statusBefore = beforeData.status;
    const statusAfter = afterData.status;
    const userId1 = afterData.userId; // User who initiated or is listed first
    const userId2 = afterData.userId2; // User who received or is listed second

    // Exit if status hasn't changed or user IDs are missing
    if (
      statusBefore === statusAfter ||
      !userId1 ||
      !userId2 ||
      userId1 === "" ||
      userId2 === ""
    ) {
      console.log(
        `Exiting: Status unchanged (${statusBefore} -> ${statusAfter}) or missing user IDs.`
      );
      return; // Use return for void functions
    }

    // Break down long log statement further
    console.log(
      `Processing update for friendship ${event.params.friendshipId}: `,
      `Status change: ${statusBefore} -> ${statusAfter}. `,
      `Users: ${userId1}, ${userId2}`
    );

    // Prepare updates using FieldValue.increment for atomicity
    const increment = admin.firestore.FieldValue.increment;

    // Reference user documents
    const user1DocRef = usersCollection.doc(userId1);
    const user2DocRef = usersCollection.doc(userId2);

    // Use a batch write for atomicity of user count updates
    const batch = db.batch();
    let updatesNeeded = false;

    // --- Logic for Status Changes ---

    // 1. Pending -> Accepted
    if (statusBefore === "pending" && statusAfter === "accepted") {
      console.log("Case: Pending -> Accepted");
      // Increment friendsCount for both users
      batch.update(user1DocRef, { friendsCount: increment(1) });
      batch.update(user2DocRef, { friendsCount: increment(1) });
      // Decrement pendingRequestCount ONLY for the user who received (userId2)
      batch.update(user2DocRef, { pendingRequestCount: increment(-1) });
      updatesNeeded = true;
    }

    // 2. Pending -> Rejected
    if (statusBefore === "pending" && statusAfter === "rejected") {
      console.log("Case: Pending -> Rejected");
      // Decrement pendingRequestCount ONLY for the user who received (userId2)
      batch.update(user2DocRef, { pendingRequestCount: increment(-1) });
      updatesNeeded = true;
    }

    // 3. Pending -> Canceled (by sender)
    if (statusBefore === "pending" && statusAfter === "canceled") {
      console.log("Case: Pending -> Canceled");
      // Decrement pendingRequestCount ONLY for the user who received (userId2)
      // Note: Assumes only sender can cancel.
      batch.update(user2DocRef, { pendingRequestCount: increment(-1) });
      updatesNeeded = true;
    }

    // 4. Accepted -> Removed (by either user)
    if (statusBefore === "accepted" && statusAfter === "removed") {
      console.log("Case: Accepted -> Removed");
      // Decrement friendsCount for both users
      batch.update(user1DocRef, { friendsCount: increment(-1) });
      batch.update(user2DocRef, { friendsCount: increment(-1) });
      updatesNeeded = true;
    }

    // --- Commit Batch ---
    if (updatesNeeded) {
      try {
        await batch.commit();
        console.log(
          `Successfully updated counts for users ${userId1} and ${userId2}.`
        );
      } catch (error) {
        console.error(
          "Error committing count updates for friendship " +
            `${event.params.friendshipId}:`,
          error
        );
        // Optional: Add more error handling/logging here if needed
      }
    } else {
      console.log("No count updates needed for this status change.");
    }

    // Functions triggered by Firestore events typically don't return anything meaningful
    return;
  }
);

/**
 * Cloud Function triggered when a document in the 'friends' collection is DELETED.
 * It decrements the 'friendsCount' on the relevant user documents IF the deleted
 * friendship had an 'accepted' status.
 */
export const handleFriendDeletion = onDocumentDeleted(
  "friends/{friendshipId}",
  // Explicitly type the event object (snapshot instead of change)
  async (
    event: FirestoreEvent<
      QueryDocumentSnapshot | undefined,
      { friendshipId: string }
    >
  ) => {
    // Exit if event data or params are missing
    if (!event.data || !event.params) {
      console.log("Exiting deletion handler: Missing event data or params.");
      return;
    }

    // Get the data of the deleted document
    const deletedData = event.data.data();

    // Exit if data is missing (shouldn't happen for onDelete)
    if (!deletedData) {
      console.log("Exiting deletion handler: Missing data snapshot.");
      return;
    }

    const status = deletedData.status;
    const userId1 = deletedData.userId;
    const userId2 = deletedData.userId2;

    // Only decrement counts if the status was 'accepted' when deleted
    if (status !== "accepted") {
      console.log(
        `Friendship ${event.params.friendshipId} deleted, ` +
          `but status was '${status}', no count update needed.`
      );
      return;
    }

    // Exit if user IDs are missing
    if (!userId1 || !userId2 || userId1 === "" || userId2 === "") {
      console.log(
        `Friendship ${event.params.friendshipId} deleted, ` +
          "but user IDs missing, no count update needed."
      );
      return;
    }

    console.log(
      `Processing deletion for accepted friendship ${event.params.friendshipId}. ` +
        `Users involved: ${userId1}, ${userId2}`
    );

    // Prepare updates using FieldValue.increment for atomicity
    const increment = admin.firestore.FieldValue.increment;

    // Reference user documents
    const user1DocRef = usersCollection.doc(userId1);
    const user2DocRef = usersCollection.doc(userId2);

    // Use a batch write for atomicity of user count updates
    const batch = db.batch();

    // Decrement friendsCount for both users
    console.log(`Decrementing friendsCount for ${userId1} and ${userId2}`);
    batch.update(user1DocRef, { friendsCount: increment(-1) });
    batch.update(user2DocRef, { friendsCount: increment(-1) });

    // --- Commit Batch ---
    try {
      await batch.commit();
      console.log(
        `Successfully decremented counts for users ${userId1} and ${userId2} after deletion.`
      );
    } catch (error) {
      console.error(
        `Error committing count updates after deletion for friendship ${event.params.friendshipId}:`,
        error
      );
    }

    return; // Indicate function finished
  }
);

/**
 * Cloud Function triggered when a document in the 'friends' collection is CREATED.
 * It increments the 'pendingRequestCount' on the recipient's user document
 * if the new friendship has a 'pending' status.
 */
export const handleFriendRequestCreation = onDocumentCreated(
  "friends/{friendshipId}",
  async (
    event: FirestoreEvent<
      QueryDocumentSnapshot | undefined,
      { friendshipId: string }
    >
  ) => {
    // --- VERY FIRST LOG ---
    logger.info(
      `FriendRequestCreation: TRIGGERED for friends/${event.params?.friendshipId}. Event ID: ${event.id}`
    );

    if (!event.data || !event.params) {
      logger.error(
        `FriendRequestCreation: EXIT (MISSING_EVENT_DATA_OR_PARAMS) for ${event.params?.friendshipId}`
      );
      return;
    }

    const createdData = event.data.data();
    const friendshipId = event.params.friendshipId; // Get here for consistent logging

    if (!createdData) {
      logger.error(
        `FriendRequestCreation: EXIT (MISSING_CREATED_DATA) for ${friendshipId}`
      );
      return;
    }

    const status = createdData.status as string | undefined;
    const senderId = createdData.userId as string | undefined;
    const recipientId = createdData.userId2 as string | undefined;

    logger.info(
      `FriendRequestCreation: DATA for ${friendshipId} - Status: ${status}, ` +
        `Sender: ${senderId}, Recipient: ${recipientId}.`
    );

    if (status !== "pending") {
      logger.info(
        `FriendRequestCreation: EXIT (STATUS_NOT_PENDING) for ${friendshipId}. Status was '${status}'.`
      );
      return;
    }

    if (!senderId || !recipientId || senderId === "" || recipientId === "") {
      logger.error(
        `FriendRequestCreation: EXIT (MISSING_USER_IDS) for ${friendshipId}. ` +
          `Sender: '${senderId}', Recipient: '${recipientId}'.`
      );
      return;
    }

    logger.info(
      `FriendRequestCreation: PASSED INITIAL CHECKS for ${friendshipId}. ` +
        "Proceeding with count and notification."
    );

    const increment = admin.firestore.FieldValue.increment;
    const recipientDocRef = usersCollection.doc(recipientId);

    // 1. Update Recipient's Pending Count
    try {
      logger.info(
        "FriendRequestCreation: Attempting to increment pending count for " +
          `recipient ${recipientId} (for ${friendshipId}).`
      );
      await recipientDocRef.update({ pendingRequestCount: increment(1) });
      logger.info(
        "FriendRequestCreation: SUCCESS - Incremented pending count for " +
          `recipient ${recipientId} (for ${friendshipId}).`
      );
    } catch (error) {
      logger.error(
        `FriendRequestCreation: ERROR - Incrementing pending count for recipient ${recipientId} (for ${friendshipId}):`,
        error
      );
    }

    // 2. Send Notification to Recipient
    try {
      logger.info(
        "FriendRequestCreation: Attempting to send notification to " +
          `${recipientId} from ${senderId} (for ${friendshipId}).`
      );

      const recipientUserDoc = await recipientDocRef.get();
      if (!recipientUserDoc.exists) {
        logger.warn(
          "FriendRequestCreation: EXIT (RECIPIENT_DOC_NOT_FOUND) for " +
            `${friendshipId}. Recipient: ${recipientId}. Cannot send notification.`
        );
        return;
      }
      const recipientUserData = recipientUserDoc.data();
      if (!recipientUserData) {
        logger.warn(
          "FriendRequestCreation: EXIT (RECIPIENT_USER_DATA_EMPTY) for " +
            `${friendshipId}. Recipient: ${recipientId}. Cannot send notification.`
        );
        return;
      }
      logger.info(
        `FriendRequestCreation: Fetched recipient ${recipientId} data for ${friendshipId}.`
      );

      const recipientFcmToken = recipientUserData.fcmToken as
        | string
        | undefined;
      const recipientNotificationSettings =
        recipientUserData.notificationSettings as
          | { notifyOnFriendRequest?: boolean }
          | undefined;

      if (recipientNotificationSettings?.notifyOnFriendRequest !== true) {
        logger.info(
          "FriendRequestCreation: EXIT (NOTIFICATIONS_DISABLED) for " +
            `${friendshipId}. Recipient: ${recipientId}. No notification sent.`
        );
        return;
      }
      logger.info(
        `FriendRequestCreation: Recipient ${recipientId} has notifications ENABLED for ${friendshipId}.`
      );

      if (!recipientFcmToken || recipientFcmToken === "") {
        logger.info(
          "FriendRequestCreation: EXIT (NO_FCM_TOKEN) for " +
            `${friendshipId}. Recipient: ${recipientId}. No notification sent.`
        );
        return;
      }
      logger.info(
        `FriendRequestCreation: Recipient ${recipientId} has FCM token for ` +
          `${friendshipId}: ${recipientFcmToken.substring(0, 20)}...`
      );

      const senderUserDoc = await usersCollection.doc(senderId).get();
      if (!senderUserDoc.exists) {
        logger.warn(
          "FriendRequestCreation: EXIT (SENDER_DOC_NOT_FOUND) for " +
            `${friendshipId}. Sender: ${senderId}. Cannot get sender name.`
        );
        return;
      }
      const senderUserData = senderUserDoc.data();
      const senderDisplayName =
        (senderUserData?.displayName as string | undefined) || "Someone";
      logger.info(
        `FriendRequestCreation: Fetched sender ${senderId} display name '${senderDisplayName}' for ${friendshipId}.`
      );

      // Construct a data-only payload
      const messagePayload: admin.messaging.TokenMessage = {
        token: recipientFcmToken,
        data: {
          type: "friend_request",
          title: "New Friend Request",
          body: `${senderDisplayName} sent you a friend request!`,
          senderName: senderDisplayName,
          senderId: senderId,
          recipientId: recipientId,
          friendshipId: friendshipId,
          // Add any other data needed by the client app
        },
        android: {
          // High priority helps ensure timely delivery on Android
          priority: "high" as const,
          // Remove the nested 'notification' block as we are sending data-only
          // notification: {
          //  channel_id: "friend_requests_channel"
          // },
        },
        // apns block could be added here for iOS specific config if needed
      };

      logger.info(
        `FriendRequestCreation: SENDING FCM to ${recipientId} for ${friendshipId}. Payload: ${JSON.stringify(
          messagePayload
        ).substring(0, 200)}...`
      );
      await admin.messaging().send(messagePayload);
      logger.info(
        `FriendRequestCreation: SUCCESS - Sent FCM to ${recipientId} for ${friendshipId}.`
      );
    } catch (error) {
      logger.error(
        `FriendRequestCreation: ERROR - Sending FCM to ${recipientId} for ${friendshipId}:`,
        error
      );
    }
    logger.info(`FriendRequestCreation: COMPLETED for ${friendshipId}.`);
    return;
  }
);

// --- NEW FUNCTION: Check Pending Email on User Creation ---

const PENDING_EMAIL_TIMEOUT_MS = 24 * 60 * 60 * 1000;

/**
 * Cloud Function triggered when a new Firebase Authentication user is created.
 * Checks if the user's email is already pending verification for another user.
 * If a recent pending verification exists for a different user, the new account is deleted.
 */
// Use the explicitly imported v1 SDK object
export const checkPendingEmailOnCreate = functionsV1.auth
  .user()
  .onCreate(async (user: UserRecord) => {
    logger.info(`New user created: UID=${user.uid}, Email=${user.email}`);

    if (!user.email) {
      logger.info(
        `User ${user.uid} created without an email. Skipping pending check.`
      );
      return null;
    }

    const normalizedEmail = user.email.toLowerCase();
    const newUserUid = user.uid;
    const pendingEmailRef = db
      .collection("pendingEmailVerifications")
      .doc(normalizedEmail);

    try {
      const pendingDoc = await pendingEmailRef.get();

      if (pendingDoc.exists) {
        const data = pendingDoc.data();
        const existingUserId = data?.userId;
        const requestedAt = data?.requestedAt as
          | admin.firestore.Timestamp
          | undefined; // Explicit cast might help

        logger.info(
          `Found pending verification for email ${normalizedEmail}. ` +
            `Requested by: ${existingUserId}, Requested at: ${requestedAt?.toDate()}`
        );

        if (existingUserId && existingUserId !== newUserUid) {
          logger.info(
            `Pending email belongs to a different user (${existingUserId}). Checking timestamp.`
          );

          if (
            requestedAt &&
            requestedAt.toMillis() > Date.now() - PENDING_EMAIL_TIMEOUT_MS
          ) {
            logger.warn(
              `CONFLICT: Email ${normalizedEmail} pending verification for user ` +
                `${existingUserId} (requested recently). Deleting newly created user ${newUserUid}.`
            );

            try {
              await admin.auth().deleteUser(newUserUid);
              logger.info(
                `Successfully deleted conflicting user ${newUserUid}.`
              );
            } catch (deleteError) {
              logger.error(
                `Failed to delete conflicting user ${newUserUid}:`,
                deleteError
              );
            }
            return null;
          } else {
            logger.info(
              `Pending verification for ${normalizedEmail} is old or timestamp missing. ` +
                `Allowing new user ${newUserUid}.`
            );
          }
        } else {
          logger.info(
            `Pending verification for ${normalizedEmail} is for the same user ` +
              `(${newUserUid}) or missing userId. Allowing creation.`
          );
        }
      } else {
        logger.info(
          `No pending verification found for email ${normalizedEmail}. Allowing user ${newUserUid}.`
        );
      }
    } catch (error) {
      logger.error(
        `Error checking pending email verification for user ${newUserUid} (email: ${normalizedEmail}):`,
        error
      );
    }

    return null;
  });

// --- END NEW FUNCTION ---

// --- NEW FUNCTION: Update Friend Document on User Visibility Change ---
export const updateFriendDocOnVisibilityChange = onDocumentUpdated(
  "users/{userId}",
  async (
    event: FirestoreEvent<
      functions.Change<QueryDocumentSnapshot> | undefined,
      { userId: string }
    >
  ) => {
    if (!event.data || !event.params) {
      logger.warn("VisibilityChange: Missing event data or params.");
      return;
    }

    const userId = event.params.userId;
    const beforeData = event.data.before.data();
    const afterData = event.data.after.data();

    if (!beforeData || !afterData) {
      logger.warn(
        `VisibilityChange: User ${userId}: Missing before or after data.`
      );
      return;
    }

    // 1. Check if 'hideOnlineStatus' actually changed and is a boolean
    if (
      beforeData.hideOnlineStatus === afterData.hideOnlineStatus ||
      typeof afterData.hideOnlineStatus !== "boolean"
    ) {
      logger.info(
        `VisibilityChange: User ${userId}: hideOnlineStatus did not change or is not a boolean. No action needed.`
      );
      return; // No return value needed for Firestore triggers
    }

    const newHideOnlineStatus = afterData.hideOnlineStatus as boolean;
    logger.info(
      `VisibilityChange: User ${userId}: hideOnlineStatus changed to ${newHideOnlineStatus}. Updating friend documents.`
    );

    const batch = db.batch();

    // 2. Query for Friend documents where the current user is 'userId'
    const friendsAsUser1Query = db
      .collection("friends")
      .where("userId", "==", userId)
      .where("status", "==", "accepted");

    // 3. Query for Friend documents where the current user is 'userId2'
    const friendsAsUser2Query = db
      .collection("friends")
      .where("userId2", "==", userId)
      .where("status", "==", "accepted");

    try {
      const [friendsAsUser1Snapshot, friendsAsUser2Snapshot] =
        await Promise.all([
          friendsAsUser1Query.get(),
          friendsAsUser2Query.get(),
        ]);

      let updatedCount = 0;

      // 4. Update documents from the first query
      friendsAsUser1Snapshot.forEach((doc) => {
        const friendDocRef = db.collection("friends").doc(doc.id);
        // Path to the specific user's flag in the map
        const fieldPath = `participantVisibilities.${userId}`;
        batch.update(friendDocRef, { [fieldPath]: newHideOnlineStatus }); // Use batch.update
        updatedCount++;
      });

      // 5. Update documents from the second query
      friendsAsUser2Snapshot.forEach((doc) => {
        const friendDocRef = db.collection("friends").doc(doc.id);
        // Path to the specific user's flag in the map
        const fieldPath = `participantVisibilities.${userId}`;
        batch.update(friendDocRef, { [fieldPath]: newHideOnlineStatus }); // Use batch.update
        updatedCount++;
      });

      // 6. Commit the batch
      if (updatedCount > 0) {
        await batch.commit();
        logger.info(
          `VisibilityChange: Successfully updated ${updatedCount} friend documents for user ${userId}.`
        );
      } else {
        logger.info(
          `VisibilityChange: No accepted friend documents found for user ${userId} to update.`
        );
      }
    } catch (error) {
      logger.error(
        `VisibilityChange: Error updating friend documents for user ${userId}:`,
        error
      );
    }
    return; // Indicate function finished
  }
);
// --- END NEW FUNCTION ---

// --- NEW FUNCTION: Sync Visibility on Friendship Change (Accepted) ---
export const syncVisibilityOnFriendshipChange = onDocumentWritten(
  "friends/{friendshipId}",
  async (
    event: FirestoreEvent<
      functions.Change<admin.firestore.DocumentSnapshot> | undefined, // Use admin.firestore.DocumentSnapshot
      { friendshipId: string }
    >
  ) => {
    logger.info(
      `SyncVisibility: Triggered for friends/${event.params.friendshipId}`
    );

    // Get data after the change (for create or update)
    const afterSnap = event.data?.after;
    const afterData = afterSnap?.data();
    // Get data before the change (for update)
    const beforeSnap = event.data?.before;
    const beforeData = beforeSnap?.data();

    // Determine if this is a relevant event:
    // 1. A new friendship created as "accepted"
    // 2. An existing friendship updated from not "accepted" to "accepted"
    const isNewAccepted = !beforeData && afterData?.status === "accepted";
    const isUpdatedToAccepted =
      beforeData?.status !== "accepted" && afterData?.status === "accepted";

    if (!isNewAccepted && !isUpdatedToAccepted) {
      logger.info(
        `SyncVisibility: Friendship ${event.params.friendshipId} not newly ` +
          `accepted or updated to accepted. Status: ${afterData?.status}. No action needed.`
      );
      return;
    }

    if (!afterData) {
      logger.warn(
        `SyncVisibility: Missing afterData for ${event.params.friendshipId}. Cannot proceed.`
      );
      return;
    }

    const user1Id = afterData.userId;
    const user2Id = afterData.userId2;

    if (!user1Id || !user2Id) {
      logger.warn(
        `SyncVisibility: Missing user1Id or user2Id in friend document ${event.params.friendshipId}.`
      );
      return;
    }

    logger.info(
      `SyncVisibility: Friendship ${event.params.friendshipId} is accepted. ` +
        `User1: ${user1Id}, User2: ${user2Id}. Fetching visibility statuses.`
    );

    try {
      // Fetch user documents in parallel
      const [user1Doc, user2Doc] = await Promise.all([
        usersCollection.doc(user1Id).get(),
        usersCollection.doc(user2Id).get(),
      ]);

      const user1HideStatus = user1Doc.exists
        ? (user1Doc.data()?.hideOnlineStatus as boolean | undefined) ?? false
        : false;
      const user2HideStatus = user2Doc.exists
        ? (user2Doc.data()?.hideOnlineStatus as boolean | undefined) ?? false
        : false;

      const participantVisibilities = {
        [user1Id]: user1HideStatus,
        [user2Id]: user2HideStatus,
      };

      logger.info(
        `SyncVisibility: Updating friends/${event.params.friendshipId} with visibilities:`,
        participantVisibilities
      );

      // Update the friend document with the visibilities
      await db
        .collection("friends")
        .doc(event.params.friendshipId)
        .update({ participantVisibilities }); // Using .update() to merge

      logger.info(
        `SyncVisibility: Successfully updated participantVisibilities for ${event.params.friendshipId}.`
      );
    } catch (error) {
      logger.error(
        `SyncVisibility: Error fetching user data or updating friend document for ${event.params.friendshipId}:`,
        error
      );
    }
    return; // Indicate function finished
  }
);
// --- END NEW FUNCTION ---

// --- NEW FUNCTION: Send Poll Invitation Notification ---
export const sendPollInvitationNotification = onDocumentCreated(
  "private_polls/{pollId}",
  async (
    event: FirestoreEvent<QueryDocumentSnapshot | undefined, { pollId: string }>
  ) => {
    logger.info(
      `PollInvitation: TRIGGERED for polls/${event.params?.pollId}. Event ID: ${event.id}`
    );

    if (!event.data || !event.params) {
      logger.error(
        `PollInvitation: EXIT (MISSING_EVENT_DATA_OR_PARAMS) for ${event.params?.pollId}`
      );
      return;
    }

    const createdPollData = event.data.data();
    const pollId = event.params.pollId;

    if (!createdPollData) {
      logger.error(`PollInvitation: EXIT (MISSING_CREATED_DATA) for ${pollId}`);
      return;
    }

    const pollType = createdPollData.type as string | undefined;
    const pollStatus = createdPollData.status as string | undefined;
    const creatorId = createdPollData.creatorId as string | undefined;
    const creatorName = createdPollData.creatorName as string | undefined;
    const pollQuestion = createdPollData.question as string | undefined;
    const isAnonymous = createdPollData.anonymous as boolean | undefined;

    // Only send for new, active, private text polls
    if (pollType !== "TEXT_PRIVATE" || pollStatus !== "ACTIVE") {
      logger.info(
        `PollInvitation: EXIT (NOT_ACTIVE_PRIVATE_TEXT_POLL) for ${pollId}. Type: ${pollType}, Status: ${pollStatus}.`
      );
      return;
    }

    if (
      !creatorId ||
      creatorId === "" ||
      !creatorName ||
      creatorName === "" ||
      !pollQuestion ||
      pollQuestion === ""
    ) {
      logger.error(
        `PollInvitation: EXIT (MISSING_POLL_METADATA) for ${pollId}. ` +
          `CreatorID: ${creatorId}, CreatorName: ${creatorName}, Question: ${pollQuestion}`
      );
      return;
    }

    logger.info(
      `PollInvitation: PASSED INITIAL CHECKS for poll ${pollId}. ` +
        `Creator: ${creatorId} (${creatorName}). Anonymous: ${isAnonymous}.`
    );

    let recipientIds: string[] = [];

    if (isAnonymous === true) {
      logger.info(
        `PollInvitation: Anonymous poll ${pollId}. Fetching all friends of creator ${creatorId}.`
      );
      try {
        const friendsQuery1 = db
          .collection("friends")
          .where("userId", "==", creatorId)
          .where("status", "==", "accepted");
        const friendsQuery2 = db
          .collection("friends")
          .where("userId2", "==", creatorId)
          .where("status", "==", "accepted");

        const [snapshot1, snapshot2] = await Promise.all([
          friendsQuery1.get(),
          friendsQuery2.get(),
        ]);

        const friendIdSet = new Set<string>();
        snapshot1.forEach((doc) => {
          const friendData = doc.data();
          if (friendData.userId2) friendIdSet.add(friendData.userId2);
        });
        snapshot2.forEach((doc) => {
          const friendData = doc.data();
          if (friendData.userId) friendIdSet.add(friendData.userId);
        });
        recipientIds = Array.from(friendIdSet);
        logger.info(
          `PollInvitation: Found ${recipientIds.length} friends for anonymous poll ${pollId}.`
        );
      } catch (error) {
        logger.error(
          `PollInvitation: ERROR fetching friends for anonymous poll ${pollId} by creator ${creatorId}:`,
          error
        );
        return; // Exit if friend fetching fails
      }
    } else {
      const allowedVoters = createdPollData.allowedVoters as
        | string[]
        | undefined;
      if (Array.isArray(allowedVoters) && allowedVoters.length > 0) {
        recipientIds = allowedVoters;
        logger.info(
          `PollInvitation: Non-anonymous poll ${pollId}. ${recipientIds.length} allowed voters specified.`
        );
      } else {
        logger.info(
          `PollInvitation: EXIT (NO_ALLOWED_VOTERS) for non-anonymous poll ${pollId}.`
        );
        return;
      }
    }

    if (recipientIds.length === 0) {
      logger.info(
        `PollInvitation: No recipients determined for poll ${pollId}. No notifications to send.`
      );
      return;
    }

    for (const recipientId of recipientIds) {
      if (recipientId === creatorId) {
        // Don't send notification to the poll creator
        logger.info(
          `PollInvitation: Skipping notification for poll ${pollId} to creator ${creatorId} themselves.`
        );
        continue;
      }

      try {
        logger.info(
          `PollInvitation: Processing recipient ${recipientId} for poll ${pollId}.`
        );
        const recipientDocRef = usersCollection.doc(recipientId);
        const recipientUserDoc = await recipientDocRef.get();

        if (!recipientUserDoc.exists) {
          logger.warn(
            `PollInvitation: RECIPIENT_DOC_NOT_FOUND for poll ${pollId}. Recipient: ${recipientId}.`
          );
          continue;
        }
        const recipientUserData = recipientUserDoc.data();
        if (!recipientUserData) {
          logger.warn(
            `PollInvitation: RECIPIENT_USER_DATA_EMPTY for poll ${pollId}. Recipient: ${recipientId}.`
          );
          continue;
        }

        const recipientFcmToken = recipientUserData.fcmToken as
          | string
          | undefined;
        const recipientNotificationSettings =
          recipientUserData.notificationSettings as
            | { pollInvitations?: boolean } // Check for pollInvitations specifically
            | undefined;

        if (recipientNotificationSettings?.pollInvitations !== true) {
          logger.info(
            `PollInvitation: NOTIFICATIONS_DISABLED (pollInvitations) for poll ${pollId}. Recipient: ${recipientId}.`
          );
          continue;
        }

        if (!recipientFcmToken || recipientFcmToken === "") {
          logger.info(
            `PollInvitation: NO_FCM_TOKEN for poll ${pollId}. Recipient: ${recipientId}.`
          );
          continue;
        }
        logger.info(
          `PollInvitation: Recipient ${recipientId} has pollInvitations ENABLED and FCM token for poll ${pollId}.`
        );

        const messagePayload: admin.messaging.TokenMessage = {
          token: recipientFcmToken,
          data: {
            type: "poll_invitation", // Specific type for poll invitations
            title: "New Poll Invitation",
            body: `${creatorName} invited you to a poll: ${pollQuestion.substring(
              0,
              100
            )}${pollQuestion.length > 100 ? "..." : ""}`, // Truncate body if too long
            pollId: pollId,
            creatorId: creatorId,
            creatorName: creatorName,
            pollQuestion: pollQuestion,
            // Add any other data needed by the client app for navigation/display
          },
          android: {
            priority: "high" as const,
          },
        };

        logger.info(
          `PollInvitation: SENDING FCM to ${recipientId} for poll ${pollId}.`
        );
        await admin.messaging().send(messagePayload);
        logger.info(
          `PollInvitation: SUCCESS - Sent FCM to ${recipientId} for poll ${pollId}.`
        );
      } catch (error) {
        logger.error(
          `PollInvitation: ERROR - Sending FCM to ${recipientId} for poll ${pollId}:`,
          error
        );
        // Continue to next recipient even if one fails
      }
    }
    logger.info(`PollInvitation: COMPLETED processing for poll ${pollId}.`);
    return;
  }
);
// --- END NEW FUNCTION ---

// --- NEW FUNCTION: Update Poll Vote Counts and VotedBy List ---
export const updatePollVoteCounts = onDocumentWritten(
  "private_polls/{pollId}/votes/{voteId}",
  async (
    event: FirestoreEvent<
      functions.Change<admin.firestore.DocumentSnapshot> | undefined,
      { pollId: string; voteId: string }
    >
  ) => {
    logger.info(
      `PollVoteUpdate: TRIGGERED for private_polls/${event.params.pollId}/votes/` +
        `${event.params.voteId}. Event ID: ${event.id}`
    );

    if (!event.data?.after.exists || event.data?.before.exists) {
      logger.info(
        `PollVoteUpdate: Event for ${event.params.pollId}/votes/${event.params.voteId} ` +
          "is not a new document creation. Skipping count update."
      );
      return;
    }

    const voteData = event.data.after.data();
    const pollId = event.params.pollId;
    const voterId = voteData?.voterId as string | undefined;

    if (!voteData || !voterId || voterId === "") {
      logger.error(
        `PollVoteUpdate: EXIT (MISSING_VOTE_DATA or VOTER_ID) for poll ${pollId}, ` +
          `vote ${event.params.voteId}. VoterId: '${voterId}'`
      );
      return;
    }

    logger.info(
      `PollVoteUpdate: New vote detected for poll ${pollId} by voter ${voterId}. Updating parent poll document.`
    );

    const pollDocRef = db.collection("private_polls").doc(pollId);

    try {
      await db.runTransaction(async (transaction) => {
        const pollDoc = await transaction.get(pollDocRef);
        if (!pollDoc.exists) {
          logger.error(
            `PollVoteUpdate: Poll document ${pollId} not found. Cannot update vote counts.`
          );
          return; // Exit transaction
        }

        const pollData = pollDoc.data();
        if (!pollData) {
          logger.error(
            `PollVoteUpdate: Poll document data for ${pollId} is empty. Cannot update vote counts.`
          );
          return; // Exit transaction
        }

        // Logic to update individual option counts
        const optionIdVoted = voteData?.optionId as string | undefined;
        let updatedOptionsArray = pollData.options; // Default to existing options array from pollData

        if (!optionIdVoted) {
          logger.error(
            `PollVoteUpdate: Vote data for ${event.params.voteId} in poll ${pollId} ` +
              "is missing 'optionId'. Individual option count will not be updated."
          );
        } else if (Array.isArray(pollData.options)) {
          let optionFoundAndUpdated = false;
          updatedOptionsArray = pollData.options.map(
            (opt: { optionId?: string; voteCount?: number }) => {
              // Ensure opt is an object and has an optionId
              if (
                typeof opt === "object" &&
                opt !== null &&
                opt.optionId === optionIdVoted
              ) {
                optionFoundAndUpdated = true;
                return {
                  ...opt,
                  // Increment voteCount, or initialize to 1 if it doesn't exist, is null, or not a number
                  voteCount:
                    (typeof opt.voteCount === "number" ? opt.voteCount : 0) + 1,
                };
              }
              return opt;
            }
          );
          if (!optionFoundAndUpdated) {
            logger.warn(
              `PollVoteUpdate: OptionId ${optionIdVoted} from vote ${event.params.voteId} ` +
                `not found in options of poll ${pollId}. Individual option count not incremented.`
            );
          }
        } else {
          logger.warn(
            `PollVoteUpdate: pollData.options for poll ${pollId} is not an array ` +
              "or is missing. Cannot update individual option counts."
          );
          // If pollData.options was not an array, keep updatedOptionsArray as it was
          // This ensures 'options: updatedOptionsArray' below doesn't write invalid data.
          // However, this means individual counts won't be updated if structure is wrong.
        }

        // Define the updates for the poll document
        const pollUpdates: {
          [key: string]: admin.firestore.FieldValue | unknown[];
        } = {
          voteCount: admin.firestore.FieldValue.increment(1),
          votedBy: admin.firestore.FieldValue.arrayUnion(voterId),
        };

        // Only include 'options' in the update if it's an array
        // This prevents errors if updatedOptionsArray became undefined
        if (Array.isArray(updatedOptionsArray)) {
          pollUpdates.options = updatedOptionsArray;
        } else if (pollData.options !== undefined) {
          // If pollData.options existed but wasn't an array,
          // we log a warning above. We avoid overwriting it with 'undefined'.
          // If it was undefined to begin with, this condition won't be met.
          logger.warn(
            `PollVoteUpdate: Not updating options field for poll ${pollId} ` +
              "as it was not an array or could not be processed."
          );
        }

        transaction.update(pollDocRef, pollUpdates);
      });

      logger.info(
        "PollVoteUpdate: SUCCESS - Updated voteCount, votedBy, and potentially " +
          `option-specific counts for poll ${pollId}.`
      );
    } catch (error) {
      logger.error(
        `PollVoteUpdate: ERROR - Transaction failed for updating poll ${pollId}:`,
        error
      );
    }
    return;
  }
);
// --- END NEW FUNCTION ---

// --- NEW FUNCTION: Send Notification When Friend is Added to Existing Poll ---
export const sendFriendAddedToPollNotification = onDocumentUpdated(
  "private_polls/{pollId}",
  async (
    event: FirestoreEvent<
      functions.Change<QueryDocumentSnapshot> | undefined,
      { pollId: string }
    >
  ) => {
    logger.info(
      `FriendAddedToPoll: TRIGGERED for poll ${event.params?.pollId}. Event ID: ${event.id}`
    );

    if (!event.data?.before || !event.data?.after || !event.params) {
      logger.error(
        `FriendAddedToPoll: EXIT (MISSING_EVENT_DATA) for ${event.params?.pollId}`
      );
      return;
    }

    const beforeData = event.data.before.data();
    const afterData = event.data.after.data();
    const pollId = event.params.pollId;

    if (!beforeData || !afterData) {
      logger.error(`FriendAddedToPoll: EXIT (MISSING_POLL_DATA) for ${pollId}`);
      return;
    }

    // Check if allowedVoters array was modified
    const beforeAllowedVoters = beforeData.allowedVoters as
      | string[]
      | undefined;
    const afterAllowedVoters = afterData.allowedVoters as string[] | undefined;

    if (
      !Array.isArray(beforeAllowedVoters) ||
      !Array.isArray(afterAllowedVoters)
    ) {
      logger.info(
        `FriendAddedToPoll: EXIT (INVALID_ALLOWED_VOTERS) for poll ${pollId}`
      );
      return;
    }

    // Find newly added friends
    const newlyAddedFriends = afterAllowedVoters.filter(
      (userId) => !beforeAllowedVoters.includes(userId)
    );

    if (newlyAddedFriends.length === 0) {
      logger.info(
        `FriendAddedToPoll: No new friends added to poll ${pollId}. Skipping notifications.`
      );
      return;
    }

    logger.info(
      `FriendAddedToPoll: Found ${
        newlyAddedFriends.length
      } newly added friends for poll ${pollId}: ${newlyAddedFriends.join(", ")}`
    );

    // Get poll details for notification
    const pollQuestion = afterData.question as string | undefined;
    const creatorId = afterData.creatorId as string | undefined;
    const creatorName = afterData.creatorName as string | undefined;

    if (!pollQuestion || !creatorId || !creatorName) {
      logger.error(
        `FriendAddedToPoll: EXIT (MISSING_POLL_DETAILS) for poll ${pollId}`
      );
      return;
    }

    // Send notifications to newly added friends
    for (const friendId of newlyAddedFriends) {
      if (friendId === creatorId) {
        // Don't send notification to the poll creator
        logger.info(
          `FriendAddedToPoll: Skipping notification for poll ${pollId} to creator ${creatorId} themselves.`
        );
        continue;
      }

      try {
        // Get friend's user data for FCM token and notification settings
        const friendUserDoc = await db.collection("users").doc(friendId).get();
        if (!friendUserDoc.exists) {
          logger.info(
            `FriendAddedToPoll: User ${friendId} not found for poll ${pollId}. Skipping notification.`
          );
          continue;
        }

        const friendUserData = friendUserDoc.data();
        if (!friendUserData) {
          logger.info(
            `FriendAddedToPoll: User data for ${friendId} is empty for poll ${pollId}. Skipping notification.`
          );
          continue;
        }

        const friendFcmToken = friendUserData.fcmToken as string | undefined;
        const friendNotificationSettings =
          friendUserData.notificationSettings as
            | { pollInvitations?: boolean }
            | undefined;

        if (friendNotificationSettings?.pollInvitations !== true) {
          logger.info(
            `FriendAddedToPoll: NOTIFICATIONS_DISABLED (pollInvitations) for poll ${pollId}. Friend: ${friendId}.`
          );
          continue;
        }

        if (!friendFcmToken || friendFcmToken === "") {
          logger.info(
            `FriendAddedToPoll: NO_FCM_TOKEN for poll ${pollId}. Friend: ${friendId}.`
          );
          continue;
        }

        logger.info(
          `FriendAddedToPoll: Friend ${friendId} has pollInvitations ENABLED and FCM token for poll ${pollId}.`
        );

        const messagePayload: admin.messaging.TokenMessage = {
          token: friendFcmToken,
          data: {
            type: "poll_invitation",
            title: "New Poll Invitation",
            body: `${creatorName} invited you to a poll: ${pollQuestion.substring(
              0,
              100
            )}${pollQuestion.length > 100 ? "..." : ""}`,
            pollId: pollId,
            creatorId: creatorId,
            creatorName: creatorName,
            pollQuestion: pollQuestion,
          },
          android: {
            priority: "high" as const,
          },
        };

        logger.info(
          `FriendAddedToPoll: SENDING FCM to ${friendId} for poll ${pollId}.`
        );
        await admin.messaging().send(messagePayload);
        logger.info(
          `FriendAddedToPoll: SUCCESS - Sent FCM to ${friendId} for poll ${pollId}.`
        );
      } catch (error) {
        logger.error(
          `FriendAddedToPoll: ERROR sending notification to ${friendId} for poll ${pollId}:`,
          error
        );
        // Continue to next friend even if one fails
      }
    }

    logger.info(
      `FriendAddedToPoll: COMPLETED processing for poll ${pollId}. Notified ${newlyAddedFriends.length} friends.`
    );
    return;
  }
);
// --- END NEW FUNCTION ---

// // Example placeholder function often included in the template:
// export const helloWorld = functions.https.onRequest((request, response) => {
//   functions.logger.info("Hello logs!", {structuredData: true});
//   response.send("Hello from Firebase!");
// });
