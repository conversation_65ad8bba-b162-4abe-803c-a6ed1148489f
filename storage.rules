rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {

    // Profile Images:
    // Allow any authenticated user to read profile images (e.g., for displaying friends' pictures).
    // Allow a user to write (create, update, delete) ONLY their own profile image.
    // Assumes the image filename is the user's UID.
    match /profile_images/{userId} {
      // Allow reading profile pictures if the user is logged in.
      allow read: if request.auth != null;

      // Allow writing (uploading, updating, deleting) the profile picture if:
      // 1. The user is logged in (request.auth != null).
      // 2. The 'userId' in the path matches the logged-in user's UID (request.auth.uid == userId).
      // 3. File size is reasonable (under 10MB) and is an image type
      allow write: if request.auth != null && request.auth.uid == userId
                   && request.resource.size < 10 * 1024 * 1024 // Limit to 10MB (more generous)
                   && (request.resource.contentType != null &&
                       (request.resource.contentType.matches('image/jpeg') ||
                        request.resource.contentType.matches('image/jpg') ||
                        request.resource.contentType.matches('image/png') ||
                        request.resource.contentType.matches('image/gif') ||
                        request.resource.contentType.matches('image/webp')));
    }

    // Poll Images:
    // This section needs further refinement once the poll creation and sharing logic is implemented.
    // We'll need rules based on poll visibility (public, private, friends) and who created the poll.
    // For now, this placeholder allows any authenticated user to read/write, similar to the initial profile image rule.
    // We will need to link this to Firestore data later to check permissions properly.
    match /poll_images/{pollId}/{fileName} {
      // TODO: Refine read access based on poll visibility (e.g., check Firestore if poll is public or user is a member).
      allow read: if request.auth != null;

      // TODO: Restrict write access to the poll creator (e.g., check Firestore for poll creator UID).
      // TODO: Add size and type checks similar to profile images.
      allow write: if request.auth != null;
    }
  }
}